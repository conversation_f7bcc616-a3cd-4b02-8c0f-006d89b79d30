import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { story, rules } = await request.json()
    
    // 使用正确的环境变量名称，匹配 .env.local 文件
    const GEMINI_API_KEY = process.env.OPENROUTER_API_KEY
    const GEMINI_API_URL = "https://openrouter.ai/api/v1/chat/completions"
    
    console.log("GEMINI_API_KEY:", GEMINI_API_KEY ? "存在" : "undefined")
    console.log("GEMINI_API_URL:", GEMINI_API_URL)
    
    if (!GEMINI_API_KEY || !GEMINI_API_URL) {
      return NextResponse.json({ error: "API KEY or URL missing" }, { status: 500 })
    }

    // 添加重试机制
    let retryCount = 0
    const maxRetries = 3
    
    while (retryCount <= maxRetries) {
      try {
        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), 90000) // 90秒超时

        console.log(`🚀 尝试API调用 (${retryCount + 1}/${maxRetries + 1})`)
        
        const res = await fetch(GEMINI_API_URL, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${GEMINI_API_KEY}`,
          },
          body: JSON.stringify({
            model: "google/gemini-2.0-flash-001",
            messages: [
              {
                role: "user",
                content: `${rules}\n\n**重要要求：请智能识别输入内容类型。如果是故事内容，根据复杂程度生成合适数量的分镜（10-25个）。如果是图片提示词列表，根据提示词数量生成对应数量的分镜。确保每个重要场景、动作、对话都有独立的分镜，不要遗漏任何关键情节。请严格按照"分镜1"、"分镜2"、"分镜3"...或"图片1"、"图片2"、"图片3"...的格式输出分镜标题。**\n\n${story}`
              }
            ],
            max_tokens: 12000,
            temperature: 0.2,
            top_p: 0.9,
            stream: false
          }),
          signal: controller.signal
        })

        clearTimeout(timeoutId)

        if (!res.ok) {
          const errText = await res.text()
          console.log(`❌ OpenRouter API错误 (${res.status}):`, errText)
          
          // 特殊处理各种错误
          if (res.status === 402) {
            return NextResponse.json({ 
              error: "API账户余额不足，请检查OpenRouter账户充值状态" 
            }, { status: 402 })
          }
          
          if (res.status === 401) {
            return NextResponse.json({ 
              error: "API密钥无效，请检查OpenRouter API Key配置" 
            }, { status: 401 })
          }
          
          if (res.status === 429) {
            if (retryCount < maxRetries) {
              console.log(`🔄 遇到限流，等待后重试...`)
              await new Promise(resolve => setTimeout(resolve, 2000 * (retryCount + 1)))
              retryCount++
              continue
            }
            return NextResponse.json({ 
              error: "API请求频率过高，请稍后重试" 
            }, { status: 429 })
          }
          
          if (res.status >= 500) {
            if (retryCount < maxRetries) {
              console.log(`🔄 服务器错误，等待后重试...`)
              await new Promise(resolve => setTimeout(resolve, 3000 * (retryCount + 1)))
              retryCount++
              continue
            }
          }
          
          return NextResponse.json({ 
            error: `OpenRouter API错误 (${res.status}): ${errText}` 
          }, { status: 500 })
        }

        const data = await res.json()
        console.log("✅ OpenRouter 响应成功")
        return NextResponse.json(data)
        
      } catch (fetchError: any) {
        console.log(`❌ 网络错误 (尝试 ${retryCount + 1}/${maxRetries + 1}):`, fetchError.message)
        
        if (fetchError.name === 'AbortError') {
          if (retryCount < maxRetries) {
            console.log(`🔄 请求超时，等待后重试...`)
            await new Promise(resolve => setTimeout(resolve, 5000 * (retryCount + 1)))
            retryCount++
            continue
          }
          return NextResponse.json({ error: "请求超时，请重试" }, { status: 408 })
        }
        
        if (retryCount < maxRetries) {
          console.log(`🔄 网络错误，等待后重试...`)
          await new Promise(resolve => setTimeout(resolve, 2000 * (retryCount + 1)))
          retryCount++
          continue
        }
        
        throw fetchError
      }
    }
    
    return NextResponse.json({ error: "所有重试都失败了" }, { status: 500 })
    
  } catch (error: any) {
    console.log("❌ Gemini API 最终错误:", error.message)
    return NextResponse.json({ error: error.message }, { status: 500 })
  }
}