'use client'

import { useState, useEffect, Suspense } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import {
  Plus,
  Minus,
  ImageIcon,
  Download,
  Video,
  Trash2,
  Edit,
  RefreshCw,
  Users,
  CheckCircle,
  Loader2,
  Eye,
  Sparkles,
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { db, type Shot, type Project } from "@/lib/database"
import { useAuth } from "@/contexts/auth-context"
import { useSearchParams, useRouter } from 'next/navigation'
import { Label } from "@/components/ui/label"

// 图片提示词着色组件
function ColoredImagePrompt({ text }: { text: string }) {
  if (!text) return <span className="text-gray-400">无图片提示词</span>

  // 定义不同内容类型的颜色映射
  const colorMap = {
    // 角色描述 - 只有这部分有颜色
    characterDescription: 'bg-blue-500/20 text-blue-200 border border-blue-500/30',
    // 默认文本 - 其他所有内容都使用这个颜色
    default: 'text-gray-300'
  }

  // 解析文本并返回着色的JSX元素
  const parseText = (text: string) => {
    const segments: React.ReactElement[] = []
    
    // 使用简单的字符串分割来查找角色描述
    // 查找包含括号和动作表情描述的文本
    const lines = text.split('\n')
    
    lines.forEach((line, lineIndex) => {
      // 检查是否包含角色描述模式
      if (line.includes('（') && line.includes('）') && 
          (line.includes('动作') || line.includes('表情') || line.includes('看') || line.includes('指') || 
           line.includes('抱') || line.includes('背') || line.includes('提') || line.includes('捧') || 
           line.includes('站') || line.includes('坐') || line.includes('走') || line.includes('跑')) &&
          (line.includes('表情') || line.includes('眼神') || line.includes('微笑') || line.includes('哭泣') || 
           line.includes('愤怒') || line.includes('悲伤') || line.includes('喜悦') || line.includes('专注') || 
           line.includes('思考') || line.includes('绝望') || line.includes('希望'))) {
        
        // 这是角色描述，需要着色
        segments.push(
          <span 
            key={`colored-${lineIndex}`} 
            className={`${colorMap.characterDescription} px-1 py-0.5 rounded text-xs block mb-1`}
          >
            {line}
          </span>
        )
      } else {
        // 这是普通文本
        if (line.trim()) {
          segments.push(
            <span key={`normal-${lineIndex}`} className={colorMap.default}>
              {line}
            </span>
          )
        }
      }
    })

    return segments.length > 0 ? segments : [
      <span key="fallback" className={colorMap.default}>{text}</span>
    ]
  }

  return (
    <div className="text-sm leading-relaxed space-y-1">
      {parseText(text)}
    </div>
  )
}

// 分镜类型（用于AI分析解析）
interface GeminiShot {
  index: number;
  content: string;
  imagePrompt: string;
  videoPrompt: string;
  subtitle: string;
}

// 首帧模式规则字符串
const GEMINI_RULES_SINGLE_FRAME = `
你是一个专业的分镜师。请将以下内容拆解成分镜头序列。

**【强制要求】数量严格匹配：**
- 如果输入包含"图片1"、"图片2"等编号，必须生成相同数量的分镜
- 输入19个图片 → 必须输出19个分镜（分镜1到分镜19）
- 输入10个图片 → 必须输出10个分镜（分镜1到分镜10）
- 这是强制性要求，不允许任何遗漏或合并
- **重要：每个图片提示词都必须对应一个独立的分镜，绝对不能合并**
- **如果输入有"图片1"到"图片19"，就必须输出"分镜1"到"分镜19"**

**【关键】智能识别与数量匹配规则：**
1. **图片提示词列表识别**：
   - 如果输入内容包含"图片1"、"图片2"、"图片3"等编号格式
   - 或者包含多个独立的图片描述段落
   - 请统计输入中的图片数量，然后生成相同数量的分镜
   - 例如：输入有19个图片提示词 → 必须输出19个分镜（分镜1到分镜19）

2. **故事内容识别**：
   - 如果输入的是连续的故事叙述
   - 请根据故事复杂程度生成合适数量的分镜（通常10-25个）

3. **严格数量对应**：
   - 图片提示词列表：输入几个图片就输出几个分镜，一一对应
   - 故事内容：根据情节复杂度灵活生成10-25个分镜
   - **绝对不能遗漏任何输入的图片提示词**

**输出格式要求：**
- 必须严格按照"分镜1"、"分镜2"、"分镜3"...的格式输出分镜标题
- 每个分镜都必须包含完整的四个部分：分镜内容、图片提示词、视频提示词、字幕

**重要指导原则：**
- 请尽可能详细地拆解内容，每个重要场景、动作、对话都应该有独立的分镜
- 对于复杂的场景，请拆解成多个分镜（例如：人物表情变化、镜头切换、动作序列等）
- **根据内容类型智能决定分镜数量：故事内容10-25个，图片提示词列表按数量对应**
- 不要省略任何重要的内容节点
- 确保每个关键情节都有对应的分镜，但也不要为了凑数量而过度拆分

你的任务是根据我提供的**内容**，将每一个场景（或一个场景内的多个关键瞬间）拆解成独立的图片，并为每张图片生成详细的、用于图像生成模型的提示词。

**视频提示词生成规则（重要）：**

请使用以下专业的单图片激活视频提示词模板：

**核心理念：场景激活三要素**
- **主体动态化**：让人物或主要物体动起来，注重微小而真实的动作
- **环境氛围化**：让背景活起来，增加画面真实感和沉浸感  
- **镜头电影化**：给静态画面增加导演视角，引导观众注意力

**通用结构：**
\`[核心画面描述] + [主体动态化指令] + [环境氛围化指令] + [镜头电影化指令] + [风格与细节指令]\`

**实用格式：**
\`[核心画面描述]，[主体动作/表情的细微变化]。[环境元素的动态效果]。[镜头运动方式]。[风格与画质]。视频画面连贯，流畅，符合现实运动规则，不要出现其他角色。\`

**各部分详解：**
1. **核心画面描述**：精准描述场景、人物、穿着、构图
2. **主体动态化**：微小真实的动作（如：她的眼神中流露出绝望、轻轻眨眼、嘴角微微抽动、一滴眼泪滑落、胸口轻微起伏等）
3. **环境氛围化**：背景元素动态化（如：暴雨持续落下、路灯光影轻轻晃动、阳光透过窗户、尘埃在光柱中飘动等）
4. **镜头电影化**：缓慢微妙的镜头运动（如：镜头极其缓慢地向前推进、向后拉远、从左向右平移、固定机位等）
5. **风格细节**：专业术语定义画面质感（如：电影感、8K画质、温暖色调、柔和光线、情绪化等）

**时间分段要求：**
- 可以在提示词中加入时间分段描述，例如：前3秒人物缓缓低头，后2秒镜头缓慢推进
- 时间分段要自然流畅，符合现实运动规律
- 每个时间段内的动作要连贯，避免突兀的跳跃

**视频提示词要求：**
- 【强制要求】必须使用中文，严禁任何英文单词，必须是完整的中文句子
- 简洁明了
- 必须严格按照上述单图片激活模板生成
- 包含五个核心要素：核心画面描述 + 主体动态化 + 环境氛围化 + 镜头电影化 + 风格细节
- 注重微小而真实的动作，避免大幅度位移
- 让背景环境也具有动态效果
- 使用缓慢微妙的镜头运动
- 适合AI视频生成工具使用
- 结尾必须包含：视频画面连贯，流畅，符合现实运动规则，不要出现其他角色

对于每个分镜，请按照以下格式输出：

**分镜1**
分镜内容：用中文描述这个分镜的场景和动作
图片提示词：[镜头类型]，[光线条件]，[时间]，在[场景描述]，背景是[背景细节描述]。

[主要角色描述1]（[人物特征]，[服装描述]）[动作描述]，[表情描述]。
[主要角色描述2]（[人物特征]，[服装描述]）[动作描述]，[表情描述]。
...（如有更多主要角色，继续添加）。

[风格]：追求极致的超写实主义照片风格。画面呈现出高度的真实感，如同顶级数码单反相机（例如佳能EOSR5或索尼Alpha1级别)搭配高质量定焦镜头（例如50mmf/1.2或85mmf/1.4)在专业布光或完美自然光条件下精心拍摄的照片。
[光照]：自然光照，柔和且均匀，微妙且真实的光影。
[色彩]：写实色调，自然色彩。逼真的色彩，准确的白平衡，避免偏黄色调。干净且平衡的色彩，不要过度饱和。
[画质]：画面高度细腻，细节极其丰富，达到照片级真实感。追求极致的清晰度和纹理表现，所有物体的材质质感都应逼真呈现。光影过渡自然平滑，色彩还原准确，无噪点，无失真，无数字感。8K分辨率视觉效果。

**重要说明：**
- 请将上述模板中的所有 [占位符] 替换为具体内容，不要保留方括号
- 镜头类型：例如：全身镜头，中景镜头，特写镜头
- 光线条件：例如：在明亮的日光下，在柔和自然光线，天空朦胧
- 时间：例如：白天，夜晚，黄昏，清晨
- 场景描述：例如：一个繁华的印度城市街道，一间破旧的印度乡村房屋内部
- 背景细节描述：例如：背景是带有"SAVCINO"和"GRIANET"标志的奢侈品店橱窗
- 主要角色描述：包含角色名称、人物特征、服装描述、动作描述、表情描述
- 特定物品或道具描述：例如：手中的一叠美元钞票，一叠叠整齐的新衣服
视频提示词：必须使用中文生成动态视频提示词，包含五个核心要素：核心画面描述+主体动态化+环境氛围化+镜头电影化+风格细节
字幕：中文对白或旁白

**分镜2**
分镜内容：用中文描述这个分镜的场景和动作
图片提示词：[镜头类型]，[光线条件]，[时间]，在[场景描述]，背景是[背景细节描述]。

[主要角色描述1]（[人物特征]，[服装描述]）[动作描述]，[表情描述]。
[主要角色描述2]（[人物特征]，[服装描述]）[动作描述]，[表情描述]。
...（如有更多主要角色，继续添加）。

[风格]：追求极致的超写实主义照片风格。画面呈现出高度的真实感，如同顶级数码单反相机（例如佳能EOSR5或索尼Alpha1级别)搭配高质量定焦镜头（例如50mmf/1.2或85mmf/1.4)在专业布光或完美自然光条件下精心拍摄的照片。
[光照]：自然光照，柔和且均匀，微妙且真实的光影。
[色彩]：写实色调，自然色彩。逼真的色彩，准确的白平衡，避免偏黄色调。干净且平衡的色彩，不要过度饱和。
[画质]：画面高度细腻，细节极其丰富，达到照片级真实感。追求极致的清晰度和纹理表现，所有物体的材质质感都应逼真呈现。光影过渡自然平滑，色彩还原准确，无噪点，无失真，无数字感。8K分辨率视觉效果。
视频提示词：必须使用中文生成动态视频提示词，包含五个核心要素：核心画面描述+主体动态化+环境氛围化+镜头电影化+风格细节
字幕：中文对白或旁白

重要要求：
- 保持角色外观、服装、场景的一致性
- 每个角色必须包含详细的外观和服装描述
- 图片提示词要遵循上述专业格式，包含镜头类型、光线氛围、人物状态、场景细节等
- 模板特点：结构化描述，专业摄影风格，高质量要求，自然色彩，细节丰富
- 视频提示词必须严格按照单图片激活模板生成，禁止使用旧格式：
  * 格式：核心画面描述，主体动作/表情的细微变化。环境元素的动态效果。镜头运动方式。风格与画质。视频画面连贯，流畅，符合现实运动规则，不要出现其他角色。
  * 示例：中景镜头，黄昏时分的柔和光线，傍晚，在印度垃圾填埋场，背景是垃圾堆和远处模糊的城市。普莉娅（20多岁，身材极其瘦削，颧骨突出，长发凌乱，面容肮脏，穿着一件褪色且打着补丁的棉布库尔蒂衫和简单的萨尔瓦裤）右手拿着一块披萨，微微向下倾斜，眼神复杂地看着流浪母猫（一只瘦弱的杂色流浪猫）和几只小猫（毛色各异，体型瘦小）围在普莉娅身边，更加靠近，抬头渴望地看着披萨。风格：追求极致的超写实主义照片风格。画面呈现出高度的真实感，如同顶级数码单反相机（例如佳能EOSR5或索尼Alpha1级别)搭配高质量定焦镜头（例如50mmf/1.2或85mmf/1.4)在专业布光或完美自然光条件下精心拍摄的照片。[光照]：自然光照，柔和且均匀，微妙且真实的光影。色彩：写实色调，自然色彩。逼真的色彩，准确的白平衡，避免偏黄色调。干净且平衡的色彩，不要过度饱和。画质：画面高度细腻，细节极其丰富，达到照片级真实感。追求极致的清晰度和纹理表现，所有物体的材质质感都应逼真呈现。光影过渡自然平滑，色彩还原准确，无噪点，无失真，无数字感。8K分辨率视觉效果。
- 【重要】视频提示词必须用中文，严禁使用英文单词，必须是完整的中文句子，描述微小而真实的动作，注重细节变化
- 请尽量生成更多分镜，不要过度简化或合并场景
- 继续编号直到故事完整结束（分镜3、分镜4...分镜N）

**【最终提醒】数量匹配检查：**
- 在开始分析前，请先统计输入内容中的图片数量
- 如果发现"图片1"、"图片2"等编号，请确保输出相同数量的分镜
- 例如：输入有图片1到图片19 → 输出必须有分镜1到分镜19
- 绝对不能遗漏任何一个图片提示词
- **重要：请在输出前再次确认分镜数量与输入图片数量完全一致**
- **如果输入有19个图片，输出必须有19个分镜，一个都不能少**
- **禁止合并多个图片为一个分镜，每个图片必须独立成为一个分镜**
- **必须从分镜1开始，连续编号到分镜19，不能跳过任何编号**
- 直接开始分析，不要询问或说明

现在请分析以下内容：
`;

// 首尾帧模式规则字符串
const GEMINI_RULES_DUAL_FRAME = `
你是一个专业的分镜师。请将以下内容拆解成分镜头序列。

**【强制要求】数量严格匹配：**
- 如果输入包含"图片1"、"图片2"等编号，必须生成相同数量的分镜
- 输入19个图片 → 必须输出19个分镜（分镜1到分镜19）
- 输入10个图片 → 必须输出10个分镜（分镜1到分镜10）
- 这是强制性要求，不允许任何遗漏或合并
- **重要：每个图片提示词都必须对应一个独立的分镜，绝对不能合并**
- **如果输入有"图片1"到"图片19"，就必须输出"分镜1"到"分镜19"**

**【关键】智能识别与数量匹配规则：**
1. **图片提示词列表识别**：
   - 如果输入内容包含"图片1"、"图片2"、"图片3"等编号格式
   - 或者包含多个独立的图片描述段落
   - 请统计输入中的图片数量，然后生成相同数量的分镜
   - 例如：输入有19个图片提示词 → 必须输出19个分镜（分镜1到分镜19）

2. **故事内容识别**：
   - 如果输入的是连续的故事叙述
   - 请根据故事复杂程度生成合适数量的分镜（通常10-25个）

3. **严格数量对应**：
   - 图片提示词列表：输入几个图片就输出几个分镜，一一对应
   - 故事内容：根据情节复杂度灵活生成10-25个分镜
   - **绝对不能遗漏任何输入的图片提示词**

**重要指导原则：**
- 请尽可能详细地拆解内容，每个重要场景、动作、对话都应该有独立的分镜
- 对于复杂的场景，请拆解成多个分镜（例如：人物表情变化、镜头切换、动作序列等）
- **根据内容类型智能决定分镜数量：故事内容10-25个，图片提示词列表按数量对应**
- 不要省略任何重要的内容节点
- 确保每个关键情节都有对应的分镜，但也不要为了凑数量而过度拆分

你的任务是根据我提供的**内容**，将每一个场景（或一个场景内的多个关键瞬间）拆解成独立的图片，并为每张图片生成详细的、用于图像生成模型的提示词。

**视频提示词生成规则（重要）：**

**分镜处理策略：**
- **前N-1个分镜**：每个分镜都有下一个分镜，使用首尾帧模板
- **最后一个分镜**：没有下一个分镜，使用单帧结束模板

请使用以下专业的首尾帧视频提示词模板：

**核心理念：首尾帧叙事**
- **起始画面**：基于第一张图片进行动态化，让静态画面活起来
- **过渡动作**：描述从第一张图片到第二张图片的过渡动作
- **结束画面**：基于第二张图片进行动态化，完成叙事闭环
- **情感氛围**：为整个视频设定情感基调

**通用结构：**
\`图片 [shot1] → 图片 [shot2]  
[前X秒：起始画面与动态描述] + [后Y秒：核心互动与过渡描述]。[整体情感与氛围描述]。[技术性指令]\`

**实用格式：**
\`图片 1 → 图片 2  
在前3秒，[起始画面描述]。[起始动态描述]。在后2秒，[核心动作描述]。[反应描述]。[镜头运动描述]。整个画面充满了[情感氛围描述]。视频画面连贯，流畅，符合现实运动规则，不要出现其他角色。\`

**各部分详解：**
1. **起始画面描述**：精准描述第一张图片的场景、人物、穿着、构图
2. **起始动态描述**：为第一张图片增加微小但合理的动作（如：眼神流转、手指轻敲、呼吸起伏）
3. **核心动作描述**：描述主动方发起的核心动作
4. **反应描述**：描绘被动方如何对这个动作做出反应
5. **镜头运动描述**：明确指示镜头的移动、推拉、摇移或焦点转移
6. **情感氛围描述**：用一句话高度概括这个场景的核心情感
7. **技术性指令**：固定的命令短语，用于提升视频质量

**时间分段要求：**
- 前X秒：基于第一张图片的动态化
- 后Y秒：从第一张图片到第二张图片的过渡
- 时间分段要自然流畅，符合现实运动规律
- 每个时间段内的动作要连贯，避免突兀的跳跃

**视频提示词要求：**
- 【强制要求】必须使用中文，严禁任何英文单词，必须是完整的中文句子
- 简洁明了
- 必须严格按照上述首尾帧模板生成
- 包含四个核心要素：起始画面动态化 + 核心互动过渡 + 情感氛围 + 技术性指令
- 注重微小而真实的动作，避免大幅度位移
- 让背景环境也具有动态效果
- 使用缓慢微妙的镜头运动
- 适合AI视频生成工具使用
- 结尾必须包含：视频画面连贯，流畅，符合现实运动规则，不要出现其他角色

对于每个分镜，请按照以下格式输出：

**分镜1**
分镜内容：用中文描述这个分镜的场景和动作
图片提示词：[镜头类型]，[光线条件]，[时间]，在[场景描述]，背景是[背景细节描述]。

[主要角色描述1]（[人物特征]，[服装描述]）[动作描述]，[表情描述]。
[主要角色描述2]（[人物特征]，[服装描述]）[动作描述]，[表情描述]。
...（如有更多主要角色，继续添加）。

[风格]：追求极致的超写实主义照片风格。画面呈现出高度的真实感，如同顶级数码单反相机（例如佳能EOSR5或索尼Alpha1级别)搭配高质量定焦镜头（例如50mmf/1.2或85mmf/1.4)在专业布光或完美自然光条件下精心拍摄的照片。
[光照]：自然光照，柔和且均匀，微妙且真实的光影。
[色彩]：写实色调，自然色彩。逼真的色彩，准确的白平衡，避免偏黄色调。干净且平衡的色彩，不要过度饱和。
[画质]：画面高度细腻，细节极其丰富，达到照片级真实感。追求极致的清晰度和纹理表现，所有物体的材质质感都应逼真呈现。光影过渡自然平滑，色彩还原准确，无噪点，无失真，无数字感。8K分辨率视觉效果。

**重要说明：**
- 请将上述模板中的所有 [占位符] 替换为具体内容，不要保留方括号
- 镜头类型：例如：全身镜头，中景镜头，特写镜头
- 光线条件：例如：在明亮的日光下，在柔和自然光线，天空朦胧
- 时间：例如：白天，夜晚，黄昏，清晨
- 场景描述：例如：一个繁华的印度城市街道，一间破旧的印度乡村房屋内部
- 背景细节描述：例如：背景是带有"SAVCINO"和"GRIANET"标志的奢侈品店橱窗
- 主要角色描述：包含角色名称、人物特征、服装描述、动作描述、表情描述
- 特定物品或道具描述：例如：手中的一叠美元钞票，一叠叠整齐的新衣服
视频提示词：[必须使用上述首尾帧模板生成动态视频提示词，中文，描述完整的动态过程和转场效果]
字幕：[中文对白或旁白]

**分镜2**
分镜内容：用中文描述这个分镜的场景和动作
图片提示词：[镜头类型]，[光线条件]，[时间]，在[场景描述]，背景是[背景细节描述]。

[主要角色描述1]（[人物特征]，[服装描述]）[动作描述]，[表情描述]。
[主要角色描述2]（[人物特征]，[服装描述]）[动作描述]，[表情描述]。
...（如有更多主要角色，继续添加）。

[风格]：追求极致的超写实主义照片风格。画面呈现出高度的真实感，如同顶级数码单反相机（例如佳能EOSR5或索尼Alpha1级别)搭配高质量定焦镜头（例如50mmf/1.2或85mmf/1.4)在专业布光或完美自然光条件下精心拍摄的照片。
[光照]：自然光照，柔和且均匀，微妙且真实的光影。
[色彩]：写实色调，自然色彩。逼真的色彩，准确的白平衡，避免偏黄色调。干净且平衡的色彩，不要过度饱和。
[画质]：画面高度细腻，细节极其丰富，达到照片级真实感。追求极致的清晰度和纹理表现，所有物体的材质质感都应逼真呈现。光影过渡自然平滑，色彩还原准确，无噪点，无失真，无数字感。8K分辨率视觉效果。
视频提示词：[必须使用上述首尾帧模板生成动态视频提示词，中文，描述完整的动态过程和转场效果]
字幕：[中文对白或旁白]

**最后一个分镜示例（分镜N）：**
分镜内容：用中文描述这个分镜的场景和动作
图片提示词：[镜头类型]，[光线条件]，[时间]，在[场景描述]，背景是[背景细节描述]。

[主要角色描述1]（[人物特征]，[服装描述]）[动作描述]，[表情描述]。
[主要角色描述2]（[人物特征]，[服装描述]）[动作描述]，[表情描述]。
...（如有更多主要角色，继续添加）。

[风格]：追求极致的超写实主义照片风格。画面呈现出高度的真实感，如同顶级数码单反相机（例如佳能EOSR5或索尼Alpha1级别)搭配高质量定焦镜头（例如50mmf/1.2或85mmf/1.4)在专业布光或完美自然光条件下精心拍摄的照片。
[光照]：自然光照，柔和且均匀，微妙且真实的光影。
[色彩]：写实色调，自然色彩。逼真的色彩，准确的白平衡，避免偏黄色调。干净且平衡的色彩，不要过度饱和。
[画质]：画面高度细腻，细节极其丰富，达到照片级真实感。追求极致的清晰度和纹理表现，所有物体的材质质感都应逼真呈现。光影过渡自然平滑，色彩还原准确，无噪点，无失真，无数字感。8K分辨率视觉效果。
视频提示词：[基于当前图片创建动态视频内容，描述画面中的微小动作和情感表达，营造故事结尾的氛围。例如：人物缓缓转身，眼神中透露着满足，嘴角微微上扬，整个画面充满了温馨的结束感。视频画面连贯，流畅，符合现实运动规则，不要出现其他角色。]
字幕：[中文对白或旁白]

重要要求：
- 保持角色外观、服装、场景的一致性
- 每个角色必须包含详细的外观和服装描述
- 图片提示词要遵循上述专业格式，包含镜头类型、光线氛围、人物状态、场景细节等
- **视频提示词生成规则（重要）：**
  - **对于前N-1个分镜**：使用首尾帧模板，格式为：图片 N → 图片 N+1 + 时间分段描述 + 情感氛围 + 技术性指令
  - **对于最后一个分镜**：使用单帧结束模板，格式为：基于当前图片创建动态视频内容，描述画面中的微小动作和情感表达，营造故事结尾的氛围
- 【重要】视频提示词必须用中文，严禁使用英文单词，必须是完整的中文句子，描述微小而真实的动作，注重细节变化
- 请尽量生成更多分镜，不要过度简化或合并场景
- 继续编号直到故事完整结束（分镜3、分镜4...分镜N）

**【最终提醒】数量匹配检查：**
- 在开始分析前，请先统计输入内容中的图片数量
- 如果发现"图片1"、"图片2"等编号，请确保输出相同数量的分镜
- 例如：输入有图片1到图片19 → 输出必须有分镜1到分镜19
- 绝对不能遗漏任何一个图片提示词
- **重要：请在输出前再次确认分镜数量与输入图片数量完全一致**
- **如果输入有19个图片，输出必须有19个分镜，一个都不能少**
- **禁止合并多个图片为一个分镜，每个图片必须独立成为一个分镜**
- **必须从分镜1开始，连续编号到分镜19，不能跳过任何编号**
- 直接开始分析，不要询问或说明

现在请分析以下内容：
`;

function StoryboardsContent() {
  const [shots, setShots] = useState<Shot[]>([])
  const [projects, setProjects] = useState<Project[]>([])
  const [selectedProjectId, setSelectedProjectId] = useState<string>("")
  const [imageSizeState, setImageSizeState] = useState("portrait-2-3")
  const [selectedImageSize, setSelectedImageSize] = useState("9:16") // 新增图片尺寸状态，默认9:16
  const [generationModel, setGenerationModel] = useState("flux") // 临时使用FLUX作为默认模型
  const [videoModel, setVideoModel] = useState("WAN") // 视频生成模型选择
  const [imageStyle, setImageStyle] = useState("realistic-photography")
  const [generationMode, setGenerationMode] = useState("smart")
  const [analysisMode, setAnalysisMode] = useState("single-frame") // 首帧/首尾帧模式选择器
  
  // 监听 analysisMode 变化
  useEffect(() => {
    console.log("🔄 analysisMode 状态变化:", analysisMode)
  }, [analysisMode])
  const [modificationText, setModificationText] = useState("")
  const [isGenerating, setIsGenerating] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [generatingImages, setGeneratingImages] = useState<Set<string>>(new Set()) // 正在生成图片的分镜ID
  const [generatingVideos, setGeneratingVideos] = useState<Set<string>>(new Set())
  const [videoQueueInfo, setVideoQueueInfo] = useState<Map<string, {position: number, estimated: string}>>(new Map()) // 正在生成视频的分镜ID
  const [editingProject, setEditingProject] = useState(false) // 项目编辑状态
  const [editingShotId, setEditingShotId] = useState<string | null>(null) // 正在编辑的分镜ID
  const [editingField, setEditingField] = useState<string | null>(null) // 正在编辑的字段
  const [editValues, setEditValues] = useState<{[key: string]: string}>({}) // 编辑值
  const [isDeleting, setIsDeleting] = useState(false) // 删除项目状态
  const [story, setStory] = useState("") // 故事输入内容
  const [isAnalyzing, setIsAnalyzing] = useState(false) // AI分析状态
  const [isModifying, setIsModifying] = useState(false) // 批量修改状态
  const [isDownloading, setIsDownloading] = useState(false) // 批量下载状态
  const [isGeneratingVideos, setIsGeneratingVideos] = useState(false) // 批量生成视频状态
  const [smartModifyingShotId, setSmartModifyingShotId] = useState<string | null>(null) // 正在智能修改的分镜ID
  const [smartModifyInput, setSmartModifyInput] = useState("") // 智能修改输入内容
  const [showSmartModifyDialog, setShowSmartModifyDialog] = useState(false) // 显示智能修改对话框
  
  // 撤销功能相关状态
  const [undoData, setUndoData] = useState<{
    type: 'single' | 'batch',
    data: Shot[] | Shot,
    timestamp: number
  } | null>(null) // 撤销数据

  // 图片尺寸选项 - 保留最常用的5种尺寸
  const imageSizeOptions = [
    { value: "1:1", label: "1:1 (方形)" },
    { value: "2:3", label: "2:3 (竖版)" },
    { value: "3:2", label: "3:2 (横版)" },
    { value: "9:16", label: "9:16 (手机竖屏)" },
    { value: "16:9", label: "16:9 (电脑横屏)" },
  ]

  const { user } = useAuth()
  const { toast } = useToast()
  const router = useRouter()
  const searchParams = useSearchParams()

  // Load projects and shots
  useEffect(() => {
    console.log("🔄 Storyboards useEffect triggered, user:", user)
    console.log("🔍 用户对象详情:", user ? { id: user.id, email: user.email } : "null")
    if (user) {
      const projectIdFromUrl = searchParams.get('project') || undefined
      console.log("📋 开始加载项目，用户ID:", user.id, "URL项目ID:", projectIdFromUrl)
      console.log("📋 用户ID长度:", user.id?.length, "格式检查:", user.id?.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i) ? "✅" : "❌")
      loadProjects(projectIdFromUrl)
    } else {
      console.log("⚠️ 用户未登录，跳过项目加载")
      setIsLoading(false)
    }
  }, [user, searchParams])

  // 当选择的项目变化时，加载分镜数据
  useEffect(() => {
    if (selectedProjectId) {
      loadShots()
    }
  }, [selectedProjectId])

  const loadProjects = async (projectIdFromUrl?: string) => {
    console.log("🚀 loadProjects 开始执行")
    setIsLoading(true)
    try {
      console.log("🔍 调用 db.getProjects，用户ID:", user?.id)
      const projectsData = await db.getProjects(user?.id || '')
      console.log("📊 获取到的项目数据:", projectsData)
      setProjects(projectsData)
      
      // 检查传入的项目ID或URL参数中是否有指定的项目ID
      const targetProjectId = projectIdFromUrl || searchParams.get('project')
      console.log("🎯 目标项目ID:", targetProjectId)
      
      if (targetProjectId && projectsData.find(p => p.id === targetProjectId)) {
        console.log("✅ 从URL选择项目:", targetProjectId)
        setSelectedProjectId(targetProjectId)
        // 保存到本地存储
        localStorage.setItem('last-selected-project', targetProjectId)
      } else if (projectsData.length > 0) {
        // 尝试从本地存储恢复上次选择的项目
        const lastSelectedProject = localStorage.getItem('last-selected-project')
        console.log("🔍 从本地存储恢复项目:", lastSelectedProject)
        
        if (lastSelectedProject && projectsData.find(p => p.id === lastSelectedProject)) {
          console.log("✅ 恢复上次选择的项目:", lastSelectedProject)
          setSelectedProjectId(lastSelectedProject)
        } else {
          // 选择最新的项目（最后一个）
          const latestProject = projectsData[projectsData.length - 1]
          console.log("📌 选择最新项目:", latestProject)
          setSelectedProjectId(latestProject.id)
          // 保存到本地存储
          localStorage.setItem('last-selected-project', latestProject.id)
        }
      } else {
        console.log("📝 没有找到项目，保持当前状态")
      }
    } catch (error) {
      console.error("❌ 加载项目失败:", error)
      toast({
        title: "Error",
        description: "Failed to load projects",
        variant: "destructive",
      })
    } finally {
      console.log("🏁 loadProjects 完成，设置 loading 为 false")
      setIsLoading(false)
    }
  }

  const loadShots = async () => {
    if (!selectedProjectId) return
    try {
      const shotsData = await db.getShots(selectedProjectId)
      console.log("📸 获取到的分镜数据:", shotsData)
      console.log("🖼️ 分镜图片URL检查:")
      shotsData.forEach((shot, index) => {
        console.log(`分镜 ${index + 1} (ID: ${shot.id}):`, {
          image_url: shot.image_url,
          status: shot.status,
          hasImage: !!shot.image_url
        })
      })
      setShots(shotsData)
    } catch (error) {
      console.error("Error loading shots:", error)
      toast({
        title: "Error",
        description: "Failed to load shots",
        variant: "destructive",
      })
    }
  }

  // 获取当前选中的项目
  const selectedProject = projects.find(p => p.id === selectedProjectId)

  const addShot = async () => {
    if (!selectedProjectId) return

    try {
      const newShot = await db.createShot({
        project_id: selectedProjectId,
        shot_number: shots.length + 1,
        title: `Scene ${shots.length + 1}`,
        content: "New scene description...",
        image_prompt: "New image prompt...",
        english_prompt: "New image prompt...",
        status: "draft",
        image_size: imageSizeState,
        generation_model: generationModel,
        image_style: imageStyle,
        generation_mode: generationMode,
      })

      setShots([...shots, newShot])
      toast({
        title: "Success",
        description: "New shot added successfully",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add shot",
        variant: "destructive",
      })
    }
  }

  const removeShot = async (shotId: string) => {
    try {
      await db.deleteShot(shotId)
      setShots(prevShots => prevShots.filter(shot => shot.id !== shotId))
      toast({
        title: "Success",
        description: "Shot removed successfully",
      })
    } catch (error) {
      console.error("Error removing shot:", error)
      toast({
        title: "Error",
        description: "Failed to remove shot",
        variant: "destructive",
      })
    }
  }

  // 项目编辑功能
  const handleProjectEdit = () => {
    setEditingProject(true)
    const currentProject = projects.find(p => p.id === selectedProjectId)
    if (currentProject) {
      setEditValues({
        title: currentProject.title,
        description: currentProject.description || ''
      })
    }
  }

  const handleProjectSave = async () => {
    try {
      const currentProject = projects.find(p => p.id === selectedProjectId)
      if (currentProject) {
        await db.updateProject(selectedProjectId, {
          title: editValues.title,
          description: editValues.description
        })
        
        // 更新本地状态
        setProjects(prevProjects => 
          prevProjects.map(p => 
            p.id === selectedProjectId 
              ? { ...p, title: editValues.title, description: editValues.description }
              : p
          )
        )
        
        setEditingProject(false)
        setEditValues({})
        
        toast({
          title: "Success",
          description: "Project updated successfully",
        })
      }
    } catch (error) {
      console.error("Error updating project:", error)
      toast({
        title: "Error",
        description: "Failed to update project",
        variant: "destructive",
      })
    }
  }

  const handleProjectCancel = () => {
    setEditingProject(false)
    setEditValues({})
  }

  // 删除项目功能
  const handleProjectDelete = async () => {
    if (!selectedProjectId) return
    
    setIsDeleting(true)
    try {
      await db.deleteProject(selectedProjectId)
      
      // 从本地状态中移除删除的项目
      const updatedProjects = projects.filter(p => p.id !== selectedProjectId)
      setProjects(updatedProjects)
      
      // 选择下一个项目或清空选择
      if (updatedProjects.length > 0) {
        setSelectedProjectId(updatedProjects[0].id)
      } else {
        setSelectedProjectId("")
        setShots([])
      }
      
      toast({
        title: "Success",
        description: "Project deleted successfully",
      })
    } catch (error) {
      console.error("Error deleting project:", error)
      toast({
        title: "Error", 
        description: "Failed to delete project",
        variant: "destructive",
      })
    } finally {
      setIsDeleting(false)
    }
  }

  // AI分析故事功能
  // 处理第二批分镜的函数
  const processSecondBatch = async (originalStory: string, projectId: string) => {
    console.log('🔄 开始处理第二批分镜...')
    console.log('📋 参数检查: originalStory长度=', originalStory.length, 'projectId=', projectId)

    try {
      // 提取第二批的图片提示词（图片11-19）
      console.log('🔍 开始提取第二批图片提示词...')
      const lines = originalStory.split('\n')
      console.log('📄 总行数:', lines.length)

      const secondBatchLines = lines.filter(line => {
        const match = line.match(/图片(\d+)/)
        if (match) {
          const num = parseInt(match[1])
          const isInRange = num >= 11 && num <= 19
          console.log(`🔍 检查行: "${line.substring(0, 50)}..." 图片编号: ${num}, 是否在范围内: ${isInRange}`)
          return isInRange
        }
        return false
      })

      const secondBatchStory = secondBatchLines.join('\n')
      console.log('📝 第二批提取的行数:', secondBatchLines.length)
      console.log('📝 第二批内容长度:', secondBatchStory.length)
      console.log('📝 第二批内容预览:', secondBatchStory.substring(0, 200) + '...')

      if (!secondBatchStory.trim()) {
        console.error('❌ 无法提取第二批图片提示词')
        throw new Error('无法提取第二批图片提示词')
      }

      const processedStory = `【批次处理】正在处理第2批（图片11-19）

原始故事背景：
${originalStory}

需要处理的图片编号：
${secondBatchStory}

请基于原始故事背景，为上述图片编号（图片11-19）生成对应的分镜，确保故事连贯性。格式：
**分镜11**
分镜内容：...
图片提示词：...
视频提示词：...
字幕：...

继续到分镜19`

      console.log('🚀 准备调用AI API...')
      console.log('📤 发送的提示词长度:', processedStory.length)

      // 调用AI API
      const result = await fetch('/api/gemini', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          story: processedStory,
          rules: GEMINI_RULES_SINGLE_FRAME
        }),
      })

      console.log('📡 API响应状态:', result.status, result.statusText)

      if (!result.ok) {
        const errorText = await result.text()
        console.error('❌ API错误响应:', errorText)
        throw new Error(`HTTP error! status: ${result.status}, message: ${errorText}`)
      }

      const data = await result.json()
      console.log("🤖 第二批AI返回的原始响应:", data)

      // 打印AI返回的文本内容用于调试
      if (data.choices?.[0]?.message?.content) {
        console.log("📝 第二批AI返回的文本内容:", data.choices[0].message.content)
      } else if (data.candidates?.[0]?.content?.parts?.[0]?.text) {
        console.log("📝 第二批AI返回的文本内容:", data.candidates[0].content.parts[0].text)
      } else {
        console.log("❌ 第二批AI响应中没有找到文本内容")
        console.log("🔍 响应结构:", JSON.stringify(data, null, 2))
      }

      // 解析第二批分镜
      console.log('🔍 开始解析第二批分镜...')
      const secondBatchShots = parseGeminiShots(data)
      console.log(`📊 第二批生成了 ${secondBatchShots.length} 个分镜`)

      if (secondBatchShots.length === 0) {
        console.log("🔍 解析失败，检查parseGeminiShots函数的输入和输出...")
        console.log("🔍 传入parseGeminiShots的data:", data)
      }

      if (secondBatchShots.length > 0) {
        console.log('💾 开始保存第二批分镜到数据库...')

        // 转换第二批分镜数据格式
        const secondBatchShotsToCreate = secondBatchShots.map((shot, index) => ({
          project_id: projectId,
          shot_number: shot.index,
          title: `分镜 ${shot.index}`,
          content: shot.content,
          image_prompt: shot.imagePrompt,
          english_prompt: shot.videoPrompt,
          video_url: undefined,
          image_url: undefined,
          duration: 5,
          shot_type: "Medium Shot",
          notes: shot.subtitle || "",
          status: "draft" as const
        }))

        // 保存第二批分镜到数据库
        await db.createShotsBatch(secondBatchShotsToCreate)
        console.log(`✅ 第二批分镜已保存到数据库`)

        console.log('🔄 重新加载项目数据...')
        // 重新加载项目数据
        await loadProjects()

        toast({
          title: "第二批分镜生成完成",
          description: `成功生成 ${secondBatchShots.length} 个分镜`,
        })
      } else {
        console.warn('⚠️ 第二批没有生成任何分镜')
        throw new Error('第二批没有生成任何分镜')
      }

    } catch (error) {
      console.error('❌ 第二批处理失败:', error)
      console.error('❌ 错误详情:', error.message)
      console.error('❌ 错误堆栈:', error.stack)
      throw error
    }
  }

  const handleAnalyze = async () => {
    if (!story.trim()) {
      toast({
        title: "错误",
        description: "请输入故事内容",
        variant: "destructive",
      })
      return
    }

    if (!user) {
      toast({
        title: "错误",
        description: "请先登录以保存分镜",
        variant: "destructive",
      })
      return
    }

    setIsAnalyzing(true)
    
    try {
      // 增加超时时间到60秒，并添加重试机制
      let retryCount = 0
      const maxRetries = 2
      
      while (retryCount <= maxRetries) {
        try {
          const controller = new AbortController()
          const timeoutId = setTimeout(() => {
            console.log('⏰ AI分析请求超时，中止请求')
            controller.abort()
          }, 60000) // 增加到60秒超时

          // 预处理输入，检查是否为图片提示词列表
          let processedStory = story
          const inputImageMatches = story.match(/图片\d+/g)
          if (inputImageMatches && inputImageMatches.length > 0) {
            const imageCount = inputImageMatches.length
            console.log(`🔍 检测到${imageCount}个图片提示词，准备分批处理`)

            // 如果图片数量超过10个，使用分批处理
            if (imageCount > 10) {
              console.log(`📦 图片数量较多(${imageCount}个)，使用分批处理策略`)

              // 分批处理：每批最多10个图片
              const batchSize = 10
              const batches = []

              for (let i = 0; i < imageCount; i += batchSize) {
                const endIndex = Math.min(i + batchSize, imageCount)
                const batchStory = story.split('\n').filter(line => {
                  const match = line.match(/图片(\d+)/)
                  if (match) {
                    const num = parseInt(match[1])
                    return num >= (i + 1) && num <= endIndex
                  }
                  return false
                }).join('\n')

                if (batchStory.trim()) {
                  batches.push({
                    startIndex: i + 1,
                    endIndex: endIndex,
                    content: batchStory
                  })
                }
              }

              console.log(`📦 分为${batches.length}批处理:`, batches.map(b => `${b.startIndex}-${b.endIndex}`))

              // 处理第一批
              if (batches.length > 0) {
                const firstBatch = batches[0]
                processedStory = `【批次处理】正在处理第1批（图片${firstBatch.startIndex}-${firstBatch.endIndex}）

${firstBatch.content}

请为上述图片生成对应的分镜，格式：
**分镜X**
分镜内容：...
图片提示词：...
视频提示词：...
字幕：...`
              }
            } else {
              // 少于10个图片，正常处理
              processedStory = `【重要】输入包含${imageCount}个图片提示词，必须生成${imageCount}个分镜！

${story}

请严格按照输入的图片提示词生成对应的分镜。`
            }

            console.log(`🔍 检测到${imageCount}个图片提示词，已在提示词中明确要求生成${imageCount}个分镜`)
          }

          const result = await fetch("/api/gemini", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              story: processedStory,
              rules: analysisMode === "dual-frame" ? GEMINI_RULES_DUAL_FRAME : GEMINI_RULES_SINGLE_FRAME
            }),
            signal: controller.signal
          })

          clearTimeout(timeoutId)
          
          if (!result.ok) {
            const errorData = await result.json()
            throw new Error(errorData.error || `HTTP ${result.status}`)
          }

          const data = await result.json()

          // 打印AI返回的原始内容用于调试
          console.log("🤖 AI返回的原始响应:", data)
          if (data.choices?.[0]?.message?.content) {
            console.log("📝 AI返回的文本内容:", data.choices[0].message.content)
          } else if (data.candidates?.[0]?.content?.parts?.[0]?.text) {
            console.log("📝 AI返回的文本内容:", data.candidates[0].content.parts[0].text)
          }

          // 解析响应
          const shots = parseGeminiShots(data)

          if (shots.length === 0) {
            throw new Error("未能解析出分镜内容，请重试")
          }

          // 检查输入是否为图片提示词列表，如果是则验证数量匹配
          if (inputImageMatches && inputImageMatches.length > 0) {
            const expectedCount = inputImageMatches.length
            const actualCount = shots.length
            console.log(`🔍 数量验证: 输入${expectedCount}个图片，生成${actualCount}个分镜`)
            console.log(`🔍 详细检查: expectedCount=${expectedCount}, actualCount=${actualCount}`)

            if (actualCount !== expectedCount) {
              console.warn(`⚠️ 分镜数量不匹配！期望${expectedCount}个，实际${actualCount}个`)

              // 检查是否是分批处理的情况
              const isBatchProcessing = actualCount === 10 && expectedCount === 19
              console.log(`🔍 分批处理检查: actualCount === 10? ${actualCount === 10}, expectedCount === 19? ${expectedCount === 19}`)
              console.log(`🔍 isBatchProcessing = ${isBatchProcessing}`)

              if (isBatchProcessing) {
                console.log(`🔧 检测到分批处理，第一批已完成，开始处理第二批...`)

                toast({
                  title: "正在处理第二批分镜",
                  description: `第一批(1-10)已完成，正在生成第二批(11-19)...`,
                })

                // 处理第二批 - 先创建项目，然后处理第二批
                try {
                  // 先创建项目和第一批分镜
                  const now = new Date()
                  const timestamp = `${now.getMonth() + 1}/${now.getDate()}/${now.getFullYear()} ${now.getHours()}:${now.getMinutes().toString().padStart(2, '0')}`
                  const project = await db.createProject({
                    user_id: user.id,
                    title: `故事分镜 - ${timestamp}`,
                    description: story.substring(0, 100) + (story.length > 100 ? "..." : ""),
                    status: "draft",
                    data: { originalStory: story }
                  })

                  // 批量创建第一批分镜数据
                  const shotsToCreate = shots.map((shot, index) => ({
                    project_id: project.id,
                    shot_number: shot.index,
                    title: `分镜 ${shot.index}`,
                    content: shot.content,
                    image_prompt: shot.imagePrompt,
                    english_prompt: shot.videoPrompt,
                    video_url: undefined,
                    image_url: undefined,
                    duration: 5,
                    shot_type: "Medium Shot",
                    notes: shot.subtitle || "",
                    status: "draft" as const
                  }))

                  await db.createShotsBatch(shotsToCreate)
                  console.log(`✅ 第一批分镜保存成功: ${shots.length} 个`)

                  // 重新加载项目数据
                  await loadProjects()

                  console.log(`🚀 调用 processSecondBatch，参数: story长度=${story.length}, projectId=${project.id}`)
                  await processSecondBatch(story, project.id)
                  console.log(`✅ processSecondBatch 执行完成`)

                  // 处理完成后再次重新加载项目数据
                  await loadProjects()

                  toast({
                    title: "分析完成",
                    description: `成功生成所有分镜`,
                  })

                  return // 提前返回，避免重复创建项目
                } catch (error) {
                  console.error('❌ 处理第二批时出错:', error)
                  toast({
                    title: "第二批处理失败",
                    description: "请手动重新生成剩余分镜",
                    variant: "destructive",
                  })
                  return // 出错时也要返回，避免重复创建项目
                }

              } else {
                console.log(`❌ 不是分批处理情况，显示错误提示`)
                toast({
                  title: "分镜数量不匹配",
                  description: `输入了${expectedCount}个图片，但只生成了${actualCount}个分镜。建议重新生成。`,
                  variant: "destructive",
                })
              }
              // 不抛出错误，允许用户查看结果并决定是否重新生成
            } else {
              console.log(`✅ 分镜数量匹配！成功生成${actualCount}个分镜`)
            }
          }

          // 如果不是分批处理，创建新项目
          const now = new Date()
          const timestamp = `${now.getMonth() + 1}/${now.getDate()}/${now.getFullYear()} ${now.getHours()}:${now.getMinutes().toString().padStart(2, '0')}`
          const project = await db.createProject({
            user_id: user.id,
            title: `故事分镜 - ${timestamp}`,
            description: story.substring(0, 100) + (story.length > 100 ? "..." : ""),
            status: "draft",
            data: { originalStory: story }
          })

          // 批量创建分镜数据
          const shotsToCreate = shots.map((shot, index) => ({
            project_id: project.id,
            shot_number: shot.index,
            title: `分镜 ${shot.index}`,
            content: shot.content,
            image_prompt: shot.imagePrompt,
            english_prompt: shot.videoPrompt,
            video_url: undefined,
            image_url: undefined,
            duration: 5,
            shot_type: "Medium Shot",
            notes: shot.subtitle || "",
            status: "draft" as const
          }))

          await db.createShotsBatch(shotsToCreate)

          // 更新项目列表和选择新项目
          await loadProjects()
          setSelectedProjectId(project.id)

          // 清空故事输入
          setStory("")

          toast({
            title: "分析完成",
            description: `成功生成 ${shots.length} 个分镜`,
          })
          
          return // 成功完成，退出重试循环
          
        } catch (fetchError: any) {
          retryCount++
          console.error(`❌ AI分析请求失败 (重试 ${retryCount}/${maxRetries}):`, fetchError.message)
          
          if (fetchError.name === 'AbortError') {
            if (retryCount <= maxRetries) {
              const retryDelay = 3000 * retryCount // 3秒、6秒递增
              console.log(`🔄 ${retryDelay/1000} 秒后重试...`)
              toast({
                title: "请求超时",
                description: `正在重试 (${retryCount}/${maxRetries})...`,
                variant: "destructive",
              })
              await new Promise(resolve => setTimeout(resolve, retryDelay))
              continue
            } else {
              throw new Error("请求超时，请检查网络连接后重试")
            }
          } else {
            // 其他错误直接抛出
            throw fetchError
          }
        }
      }
        
    } catch (error: any) {
      console.error("分析失败:", error)
      
      let errorMessage = "分析失败，请重试"
      if (error.message.includes("超时")) {
        errorMessage = "请求超时，请检查网络连接后重试"
      } else if (error.message.includes("API")) {
        errorMessage = "API 服务暂时不可用，请稍后重试"
      } else if (error.message.includes("登录")) {
        errorMessage = "请先登录以保存分镜结果"
      }
      
      toast({
        title: "分析失败",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setIsAnalyzing(false)
    }
  }

  // 解析 Gemini 返回的分镜信息
  function parseGeminiShots(apiResponse: any): GeminiShot[] {
    try {
      let text = ""
      if (apiResponse.choices && apiResponse.choices[0]?.message?.content) {
        text = apiResponse.choices[0].message.content
      } else if (apiResponse.candidates && apiResponse.candidates[0]?.content?.parts?.[0]?.text) {
        text = apiResponse.candidates[0].content.parts[0].text
      } else {
        return []
      }

      const shots: GeminiShot[] = []
      const lines = text.split('\n')
      let currentShot: Partial<GeminiShot> = {}
      let currentSection = ""
      let shotIndex = 0
      let isFirstShot = true

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim()
        
        if (line.match(/^\*\*分镜\s*\d+/i) || line.match(/^分镜\s*\d+/i) || line.match(/^\d+[\.\、]/)) {
          // 保存上一个分镜（如果有内容且不是第一次遇到分镜标题）
          if (!isFirstShot && (currentShot.content || currentShot.imagePrompt || currentShot.videoPrompt)) {
            shots.push({
              index: shotIndex,
              content: currentShot.content || "",
              imagePrompt: currentShot.imagePrompt || "",
              videoPrompt: currentShot.videoPrompt || "",
              subtitle: currentShot.subtitle || ""
            })
          }
          
          // 开始新分镜
          currentShot = {}
          currentSection = ""
          isFirstShot = false
          
          // 从分镜标题中提取序号
          const match = line.match(/分镜\s*(\d+)/i)
          if (match) {
            shotIndex = parseInt(match[1])
          } else {
            // 如果没有匹配到分镜序号，使用默认递增
            shotIndex = shots.length + 1
          }
          continue
        }
        
        if (line.includes("分镜内容") || line.includes("内容")) {
          currentSection = "content"
          const content = line.split(/[:：]/)[1]?.trim()
          if (content) currentShot.content = content
        } else if (line.includes("图片提示词") || line.includes("图片提示")) {
          currentSection = "imagePrompt"
          const prompt = line.split(/[:：]/)[1]?.trim()
          if (prompt) currentShot.imagePrompt = prompt
        } else if (line.includes("视频提示词") || line.includes("视频提示")) {
          currentSection = "videoPrompt"
          const prompt = line.split(/[:：]/)[1]?.trim()
          if (prompt) currentShot.videoPrompt = prompt
        } else if (line.includes("字幕") || line.includes("subtitle")) {
          currentSection = "subtitle"
          const subtitle = line.split(/[:：]/)[1]?.trim()
          if (subtitle) currentShot.subtitle = subtitle
        } else {
          if (currentSection === "content") {
            currentShot.content = (currentShot.content || "") + " " + line.trim()
          } else if (currentSection === "imagePrompt") {
            currentShot.imagePrompt = (currentShot.imagePrompt || "") + " " + line.trim()
          } else if (currentSection === "videoPrompt") {
            currentShot.videoPrompt = (currentShot.videoPrompt || "") + " " + line.trim()
          } else if (currentSection === "subtitle") {
            currentShot.subtitle = (currentShot.subtitle || "") + " " + line.trim()
          }
        }
      }

      // 保存最后一个分镜
      if (currentShot.content || currentShot.imagePrompt || currentShot.videoPrompt) {
        shots.push({
          index: shotIndex,
          content: currentShot.content || "",
          imagePrompt: currentShot.imagePrompt || "",
          videoPrompt: currentShot.videoPrompt || "",
          subtitle: currentShot.subtitle || ""
        })
      }

      return shots

    } catch (error) {
      console.error("解析分镜时出错:", error)
      return []
    }
  }

  // 批量修改分镜功能
  const handleSmartModify = async () => {
    if (!modificationText.trim()) {
      toast({
        title: "错误",
        description: "请输入修改指令",
        variant: "destructive",
      })
      return
    }

    if (!user) {
      toast({
        title: "错误",
        description: "请先登录",
        variant: "destructive",
      })
      return
    }

    if (shots.length === 0) {
      toast({
        title: "错误",
        description: "没有可修改的分镜",
        variant: "destructive",
      })
      return
    }

    setIsModifying(true)
    
    try {
      // 保存原始数据用于撤销
      setUndoData({
        type: 'batch',
        data: [...shots], // 深拷贝所有镜头数据
        timestamp: Date.now()
      })
      
      // 构建修改指令的prompt  
      const modificationPrompt = `
我需要你修改现有的分镜内容，而不是创建新的分镜。

修改要求：${modificationText}

待修改的分镜列表（共${shots.length}个）：
${shots.map((shot, index) => `
**分镜${index + 1}**
分镜内容：${shot.content}
图片提示词：${shot.image_prompt}
视频提示词：${shot.english_prompt}
字幕：${shot.notes}
`).join('\n')}

请根据修改要求，对每个分镜进行相应调整，然后按照完全相同的格式输出修改后的${shots.length}个分镜：
`

      const controller = new AbortController()
      const timeoutId = setTimeout(() => {
        controller.abort()
        toast({
          title: "请求超时",
          description: "API 响应时间过长，请重试",
          variant: "destructive",
        })
      }, 35000)

      const result = await fetch("/api/gemini", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          story: modificationPrompt,
          rules: "你是专业的分镜修改师。严格按照用户的修改指令对现有分镜进行调整，保持格式一致，直接输出修改结果，不要添加任何解释。" // 明确的修改角色定义
        }),
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      if (!result.ok) {
        const errorData = await result.json()
        throw new Error(errorData.error || `HTTP ${result.status}`)
      }

      const data = await result.json()
      
      // 打印AI返回的原始内容用于调试
      console.log("AI返回的原始响应:", data)
      if (data.choices?.[0]?.message?.content) {
        console.log("AI返回的文本内容:", data.choices[0].message.content)
      }

      // 解析修改后的分镜
      const modifiedShots = parseGeminiShots(data)
      
      if (modifiedShots.length === 0) {
        const aiResponse = data.choices?.[0]?.message?.content || "无响应内容"
        console.error("AI返回内容解析失败，原始内容:", aiResponse)
        
        // 检查AI是否误解了任务
        if (aiResponse.includes("请提供") || aiResponse.includes("我将") || aiResponse.includes("好的")) {
          throw new Error("AI误解了修改任务。请尝试使用更明确的修改指令，如：'将所有场景改为夜晚'、'增加雨效'等")
        } else {
          throw new Error("未能解析修改后的分镜内容。AI返回格式不正确，请重试")
        }
      }

      // 更新现有分镜
      const updatePromises = modifiedShots.map(async (modifiedShot, index) => {
        if (index < shots.length) {
          const originalShot = shots[index]
          return await db.updateShot(originalShot.id, {
            content: modifiedShot.content,
            image_prompt: modifiedShot.imagePrompt,
            english_prompt: modifiedShot.videoPrompt,
            notes: modifiedShot.subtitle
          })
        }
      })

      await Promise.all(updatePromises.filter(Boolean))

      // 重新加载分镜数据
      await loadShots()

      // 清空修改指令
      setModificationText("")

      toast({
        title: "修改完成",
        description: `成功修改 ${Math.min(modifiedShots.length, shots.length)} 个分镜`,
      })
        
    } catch (error: any) {
      console.error("批量修改失败:", error)
      
      if (error.name === 'AbortError') {
        setIsModifying(false)
        return
      }
      
      let errorMessage = "修改失败，请重试"
      if (error.message.includes("超时")) {
        errorMessage = "请求超时，请检查网络连接后重试"
      } else if (error.message.includes("API")) {
        errorMessage = "API 服务暂时不可用，请稍后重试"
      }
      
      toast({
        title: "修改失败",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setIsModifying(false)
    }
  }

  // 分镜编辑功能
  const handleShotEdit = (shotId: string, field: string, value: string) => {
    setEditingShotId(shotId)
    setEditingField(field)
    setEditValues({ [field]: value })
  }

  const handleShotSave = async () => {
    if (!editingShotId || !editingField) return
    
    try {
      const updateData: any = {}
      updateData[editingField] = editValues[editingField]
      
      await db.updateShot(editingShotId, updateData)
      
      // 更新本地状态
      setShots(prevShots => 
        prevShots.map(shot => 
          shot.id === editingShotId 
            ? { ...shot, ...updateData }
            : shot
        )
      )
      
      setEditingShotId(null)
      setEditingField(null)
      setEditValues({})
      
      toast({
        title: "Success",
        description: "Shot updated successfully",
      })
    } catch (error) {
      console.error("Error updating shot:", error)
      toast({
        title: "Error",
        description: "Failed to update shot",
        variant: "destructive",
      })
    }
  }

  const handleShotCancel = () => {
    setEditingShotId(null)
    setEditingField(null)
    setEditValues({})
  }

  // 打开单个Shot智能修改对话框
  const openSmartModifyDialog = (shotId: string) => {
    setSmartModifyingShotId(shotId)
    setShowSmartModifyDialog(true)
    setSmartModifyInput("")
  }

  // 关闭智能修改对话框
  const closeSmartModifyDialog = () => {
    setShowSmartModifyDialog(false)
    setSmartModifyingShotId(null)
    setSmartModifyInput("")
  }

  // 恢复功能
  const handleUndo = async () => {
    if (!undoData || !user) {
      toast({
        title: "无法恢复",
        description: "没有可恢复的数据",
        variant: "destructive",
      })
      return
    }

    try {
      if (undoData.type === 'single') {
        // 恢复单个镜头
        const originalShot = undoData.data as Shot
        await db.updateShot(originalShot.id, {
          content: originalShot.content,
          image_prompt: originalShot.image_prompt,
          english_prompt: originalShot.english_prompt,
          notes: originalShot.notes,
        })

        // 更新本地状态
        setShots(prevShots => 
          prevShots.map(shot => 
            shot.id === originalShot.id ? originalShot : shot
          )
        )

        toast({
          title: "恢复成功",
          description: "镜头内容已恢复到修改前的状态",
        })

        // 关闭单个镜头修改对话框
        closeSmartModifyDialog()

      } else if (undoData.type === 'batch') {
        // 恢复所有镜头
        const originalShots = undoData.data as Shot[]
        
        // 批量更新数据库
        const updatePromises = originalShots.map(shot => 
          db.updateShot(shot.id, {
            content: shot.content,
            image_prompt: shot.image_prompt,
            english_prompt: shot.english_prompt,
            notes: shot.notes,
          })
        )
        
        await Promise.all(updatePromises)

        // 更新本地状态
        setShots(originalShots)

        toast({
          title: "恢复成功",
          description: "所有镜头内容已恢复到修改前的状态",
        })
      }

      // 清除撤销数据
      setUndoData(null)

    } catch (error: any) {
      console.error("恢复失败:", error)
      toast({
        title: "恢复失败",
        description: error.message || "恢复过程中出现错误",
        variant: "destructive",
      })
    }
  }

  // 清除撤销数据
  const clearUndoData = () => {
    setUndoData(null)
  }

  // 执行单个Shot的智能修改
  const handleSingleShotSmartModify = async () => {
    if (!smartModifyingShotId || !smartModifyInput.trim()) {
      toast({
        title: "错误",
        description: "请输入修改指令",
        variant: "destructive",
      })
      return
    }

    const targetShot = shots.find(shot => shot.id === smartModifyingShotId)
    if (!targetShot) {
      toast({
        title: "错误", 
        description: "找不到目标分镜",
        variant: "destructive",
      })
      return
    }

    try {
      setIsModifying(true)
      
      // 保存原始数据用于撤销
      setUndoData({
        type: 'single',
        data: { ...targetShot }, // 深拷贝原始镜头数据
        timestamp: Date.now()
      })
      
      // 构建智能修改提示词
      const modifyPrompt = `
你是一个专业的分镜师。请根据修改指令智能修改这个分镜。

原始分镜：
分镜内容：${targetShot.content}
图片提示词：${targetShot.image_prompt}
视频提示词：${targetShot.english_prompt}

修改指令：${smartModifyInput}

要求：
1. 根据修改指令调整分镜内容、图片提示词和视频提示词
2. 图片提示词要包含镜头类型、光线氛围、人物状态、场景细节
3. 视频提示词要简洁英文，包含动作和镜头运动
4. 保持专业分镜格式

请严格按照以下格式输出，不要添加其他内容：

分镜内容：修改后的分镜内容描述
图片提示词：修改后的详细图片提示词
视频提示词：修改后的英文视频提示词

请立即开始修改：
`

      const response = await fetch("/api/gemini", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          story: modifyPrompt,
          rules: ""
        }),
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }

      const data = await response.json()
      
      // 解析AI返回的修改内容
      const content = data.choices?.[0]?.message?.content || ""
      console.log("AI修改响应:", content)
      
      // 解析修改后的内容
      let newContent = targetShot.content
      let newImagePrompt = targetShot.image_prompt
      let newVideoPrompt = targetShot.english_prompt

      if (!content) {
        throw new Error("AI返回内容为空")
      }

      // 使用更宽松的正则表达式提取修改后的内容
      const contentMatch = content.match(/(?:分镜内容：|内容：)\s*([\s\S]*?)(?=(?:图片提示词：|提示词：)|$)/)
      const imagePromptMatch = content.match(/(?:图片提示词：|提示词：)\s*([\s\S]*?)(?=(?:视频提示词：|Video)|$)/)
      const videoPromptMatch = content.match(/(?:视频提示词：|Video|英文提示词：)\s*([\s\S]*?)(?=\n\n|$)/)

      console.log("解析结果:", {
        contentMatch: contentMatch ? contentMatch[1].substring(0, 100) + "..." : null,
        imagePromptMatch: imagePromptMatch ? imagePromptMatch[1].substring(0, 100) + "..." : null,
        videoPromptMatch: videoPromptMatch ? videoPromptMatch[1].substring(0, 100) + "..." : null
      })

      if (contentMatch) {
        newContent = contentMatch[1].trim()
      }
      if (imagePromptMatch) {
        newImagePrompt = imagePromptMatch[1].trim()
      }
      if (videoPromptMatch) {
        newVideoPrompt = videoPromptMatch[1].trim()
      }

      // 检查是否至少有一个字段被修改
      if (newContent === targetShot.content && 
          newImagePrompt === targetShot.image_prompt && 
          newVideoPrompt === targetShot.english_prompt) {
        console.warn("没有检测到任何修改，使用备用解析方法")
        // 尝试更简单的解析方法
        const lines = content.split('\n').filter((line: string) => line.trim())
        for (let i = 0; i < lines.length; i++) {
          const line = lines[i].trim()
          if (line.includes('分镜内容') || line.includes('内容')) {
            const nextLine = lines[i + 1]
            if (nextLine && !nextLine.includes('：')) {
              newContent = nextLine.trim()
            }
          } else if (line.includes('图片提示词') || line.includes('提示词')) {
            const nextLine = lines[i + 1]
            if (nextLine && !nextLine.includes('：')) {
              newImagePrompt = nextLine.trim()
            }
          } else if (line.includes('视频提示词') || line.includes('Video')) {
            const nextLine = lines[i + 1]
            if (nextLine && !nextLine.includes('：')) {
              newVideoPrompt = nextLine.trim()
            }
          }
        }
      }

      console.log("最终修改结果:", {
        originalContent: targetShot.content,
        newContent,
        originalImagePrompt: targetShot.image_prompt?.substring(0, 100) + "..." || "无",
        newImagePrompt: newImagePrompt?.substring(0, 100) + "..." || "无",
        originalVideoPrompt: targetShot.english_prompt,
        newVideoPrompt
      })

      // 更新数据库
      await db.updateShot(smartModifyingShotId, {
        content: newContent,
        image_prompt: newImagePrompt,
        english_prompt: newVideoPrompt,
      })

      // 更新本地状态
      setShots(prevShots => 
        prevShots.map(shot => 
          shot.id === smartModifyingShotId 
            ? { ...shot, content: newContent, image_prompt: newImagePrompt, english_prompt: newVideoPrompt }
            : shot
        )
      )

      toast({
        title: "智能修改完成",
        description: `分镜内容已成功修改。${newContent !== targetShot.content ? '内容已更新' : ''}${newImagePrompt !== targetShot.image_prompt ? ' 图片提示词已更新' : ''}${newVideoPrompt !== targetShot.english_prompt ? ' 视频提示词已更新' : ''}`,
      })

      closeSmartModifyDialog()

    } catch (error: any) {
      console.error("智能修改失败:", error)
      toast({
        title: "修改失败",
        description: error.message || "智能修改过程中出现错误",
        variant: "destructive",
      })
    } finally {
      setIsModifying(false)
    }
  }

  // 根据图片尺寸获取预览样式
  const getImagePreviewStyle = (imageSize: string) => {
    const sizeMapping: Record<string, { width: string; height: string; aspectRatio: string }> = {
      "1:1": { width: "w-64", height: "h-64", aspectRatio: "aspect-square" },
      "2:3": { width: "w-48", height: "h-72", aspectRatio: "aspect-[2/3]" },
      "3:2": { width: "w-72", height: "h-48", aspectRatio: "aspect-[3/2]" },
      "3:4": { width: "w-48", height: "h-64", aspectRatio: "aspect-[3/4]" },
      "4:3": { width: "w-64", height: "h-48", aspectRatio: "aspect-[4/3]" },
      "9:16": { width: "w-36", height: "h-64", aspectRatio: "aspect-[9/16]" },
      "16:9": { width: "w-64", height: "h-36", aspectRatio: "aspect-[16/9]" },
      "9:21": { width: "w-32", height: "h-72", aspectRatio: "aspect-[9/21]" },
      "2:1": { width: "w-72", height: "h-36", aspectRatio: "aspect-[2/1]" },
      // 支持演示数据中的格式
      "portrait-2-3": { width: "w-48", height: "h-72", aspectRatio: "aspect-[2/3]" },
      "landscape-3-2": { width: "w-72", height: "h-48", aspectRatio: "aspect-[3/2]" },
      "square-1-1": { width: "w-64", height: "h-64", aspectRatio: "aspect-square" },
      "portrait-9-16": { width: "w-36", height: "h-64", aspectRatio: "aspect-[9/16]" },
      "landscape-16-9": { width: "w-64", height: "h-36", aspectRatio: "aspect-[16/9]" },
    }
    
    return sizeMapping[imageSize] || sizeMapping["1:1"]
  }

  // 下载图片功能
  const downloadImage = async (imageUrl: string, shotNumber: number) => {
    try {
      console.log("尝试下载图片:", imageUrl)
      
      // 方法1: 检查是否是本地图片
      if (imageUrl.startsWith('/images/')) {
        console.log("检测到本地图片，直接下载")
        const link = document.createElement('a')
        link.href = imageUrl
        link.download = `shot-${shotNumber}-${Date.now()}.png`
        link.target = '_blank'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        
        toast({
          title: "下载已开始",
          description: "本地图片下载已触发",
        })
        return
      }
      
      // 方法2: 尝试通过代理API下载远程图片
      try {
        const response = await fetch('/api/download-image', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ imageUrl })
        })
        
        if (response.ok) {
          const blob = await response.blob()
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = `shot-${shotNumber}-${Date.now()}.png`
          document.body.appendChild(a)
          a.click()
          window.URL.revokeObjectURL(url)
          document.body.removeChild(a)
          
          toast({
            title: "Success",
            description: "Image downloaded successfully",
          })
          return
        }
      } catch (proxyError) {
        console.log("代理下载失败，尝试直接下载:", proxyError)
      }
      
      // 方法3: 尝试直接fetch（可能受跨域限制）
      try {
        const response = await fetch(imageUrl, {
          mode: 'cors',
          headers: {
            'Access-Control-Allow-Origin': '*'
          }
        })
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `shot-${shotNumber}-${Date.now()}.png`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
        
        toast({
          title: "Success",
          description: "Image downloaded successfully",
        })
        return
      } catch (corsError) {
        console.log("直接下载失败，使用新窗口下载:", corsError)
      }
      
      // 方法4: 在新窗口打开图片（用户可以右键保存）
      const link = document.createElement('a')
      link.href = imageUrl
      link.target = '_blank'
      link.download = `shot-${shotNumber}-${Date.now()}.png`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      toast({
        title: "Image opened",
        description: "Image opened in new tab. Right-click to save.",
      })
      
    } catch (error) {
      console.error("Error downloading image:", error)
      toast({
        title: "Error",
        description: "Failed to download image. Please try right-clicking the image to save.",
        variant: "destructive",
      })
    }
  }

  // 下载视频函数
  const downloadVideo = async (videoUrl: string, shotNumber: number) => {
    try {
      console.log("尝试下载视频:", videoUrl)
      
      // 方法1: 检查是否是本地视频
      if (videoUrl.startsWith('/videos/')) {
        console.log("检测到本地视频，直接下载")
        const link = document.createElement('a')
        link.href = videoUrl
        link.download = `shot-${shotNumber}-video-${Date.now()}.mp4`
        link.target = '_blank'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        
        toast({
          title: "下载已开始",
          description: "本地视频下载已触发",
        })
        return
      }
      
      // 方法2: 尝试通过代理API下载远程视频
      try {
        const response = await fetch('/api/download-video', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ videoUrl })
        })
        
        if (response.ok) {
          const blob = await response.blob()
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = `shot-${shotNumber}-video-${Date.now()}.mp4`
          document.body.appendChild(a)
          a.click()
          window.URL.revokeObjectURL(url)
          document.body.removeChild(a)
          
          toast({
            title: "Success",
            description: "Video downloaded successfully",
          })
          return
        }
      } catch (proxyError) {
        console.log("代理下载失败，尝试直接下载:", proxyError)
      }
      
      // 方法3: 尝试直接fetch（可能受跨域限制）
      try {
        const response = await fetch(videoUrl, {
          mode: 'cors',
          headers: {
            'Access-Control-Allow-Origin': '*'
          }
        })
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `shot-${shotNumber}-video-${Date.now()}.mp4`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
        
        toast({
          title: "Success",
          description: "Video downloaded successfully",
        })
        return
      } catch (corsError) {
        console.log("直接下载失败，使用新窗口下载:", corsError)
      }
      
      // 方法4: 在新窗口打开视频（用户可以右键保存）
      const link = document.createElement('a')
      link.href = videoUrl
      link.target = '_blank'
      link.download = `shot-${shotNumber}-video-${Date.now()}.mp4`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      toast({
        title: "Video opened",
        description: "Video opened in new tab. Right-click to save.",
      })
      
    } catch (error) {
      console.error("Error downloading video:", error)
      toast({
        title: "Error",
        description: "Failed to download video. Please try right-clicking the video to save.",
        variant: "destructive",
      })
    }
  }

  const downloadRunwayVideo = async (taskId: string, shotNumber: number) => {
    try {
      console.log("尝试下载RUNWAY视频，任务ID:", taskId)
      
      // 调用RUNWAY API获取下载信息
      const response = await fetch(`/api/runway/download?task_id=${taskId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      })
      
      if (!response.ok) {
        throw new Error('Failed to get RUNWAY video download information')
      }
      
      const data = await response.json()
      
      if (data.success && data.video_url) {
        // 如果有真实的视频URL，直接下载
        console.log("找到真实视频URL，开始下载:", data.video_url)
        
        const link = document.createElement('a')
        link.href = data.video_url
        link.download = `shot-${shotNumber}-runway-video.mp4`
        link.target = '_blank'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        
        toast({
          title: "下载成功",
          description: `RUNWAY视频已开始下载`,
        })
      } else if (data.video_url) {
        // 如果有模拟的下载链接，跳转到RUNWAY官网
        console.log("跳转到RUNWAY官网下载:", data.video_url)
        
        window.open(data.video_url, '_blank')
        
        toast({
          title: "跳转到RUNWAY官网",
          description: "正在打开RUNWAY官网，请手动下载视频",
          duration: 5000,
        })
      } else {
        // 显示下载指导
        toast({
          title: "RUNWAY视频下载指导",
          description: (
            <div className="space-y-2">
              <p className="font-medium">任务ID: {taskId}</p>
              <div className="text-sm space-y-1">
                {data.instructions?.map((instruction: string, index: number) => (
                  <p key={index} className="text-gray-300">{instruction}</p>
                ))}
              </div>
              <p className="text-xs text-gray-400 mt-2">{data.note}</p>
            </div>
          ),
          duration: 10000,
        })
      }
      
    } catch (error) {
      console.error("下载RUNWAY视频失败:", error)
      toast({
        title: "下载指导",
        description: `请登录RUNWAY官网 (https://runway.ml) 查找任务ID: ${taskId} 并手动下载视频`,
        duration: 8000,
      })
    }
  }

  // 生成单个分镜的视频
  const generateVideo = async (shotId: string, imageUrl: string, videoPrompt: string) => {
    if (!imageUrl?.trim()) {
      toast({
        title: "错误",
        description: "需要先生成图片才能生成视频",
        variant: "destructive",
      })
      return
    }

    if (!videoPrompt?.trim()) {
      toast({
        title: "错误", 
        description: "视频提示词不能为空",
        variant: "destructive",
      })
      return
    }

    // 根据分析模式调整视频提示词
    let adjustedVideoPrompt = videoPrompt
    
    console.log("🔍 视频生成时的analysisMode:", analysisMode)
    console.log("🔍 当前videoPrompt:", videoPrompt)
    console.log("🔍 当前shots数组长度:", shots.length)
    console.log("🔍 当前shots数组内容:", shots.map(s => ({ id: s.id, hasImagePrompt: !!s.image_prompt })))
    
    if (analysisMode === "dual-frame") {
      // 首尾帧模式：分析两个相邻的Shot并生成视频提示词
      console.log("🎭 检测到首尾帧模式，analysisMode:", analysisMode)
      const currentShotIndex = shots.findIndex(s => s.id === shotId)
      const nextShot = shots[currentShotIndex + 1]
      
      console.log("📊 当前Shot索引:", currentShotIndex)
      console.log("📊 总Shots数量:", shots.length)
      console.log("📊 所有Shots:", shots.map(s => ({ id: s.id, hasImagePrompt: !!s.image_prompt })))
      console.log("📊 下一个Shot:", nextShot ? nextShot.id : "无")
      console.log("📊 下一个Shot的image_prompt:", nextShot?.image_prompt ? "存在" : "不存在")
      
      if (nextShot && nextShot.image_prompt) {
        // 有下一个Shot，使用首尾帧模板
        const shot1Prompt = shots.find(s => s.id === shotId)?.image_prompt || ""
        const shot2Prompt = nextShot.image_prompt
        
        console.log("🎬 首尾帧模式：分析两个Shot")
        console.log("📝 Shot1 图片提示词:", shot1Prompt)
        console.log("📝 Shot2 图片提示词:", shot2Prompt)
        
        // 使用新的首尾帧模板
        adjustedVideoPrompt = `场景描述：从 [场景A的核心概念] 过渡到 [场景B的核心概念]
[前X秒：起始画面与动态描述] + [后Y秒：核心互动与过渡描述]。[整体情感与氛围描述]。[技术性指令]

**模板详解：**
此模板专为将两个独立的图片生成提示词（即首尾帧的概念描述）融合成一个动态视频脚本而设计。

**第一步：分析源提示词，确立核心变化**
在填充模板前，先解读您已有的两个图片提示词，找出从场景A到场景B的核心变化。这个变化是故事的关键，可能是：
- 动作变化：从等待变为出现。
- 情绪转变：从忧虑变为绝望。
- 焦点转移：镜头从A角色移到B角色。

**第二步：填充模板各部分**
- **[场景描述]**：概括视频的核心故事线。基于对"核心变化"的分析，用一句话描述这个过渡。
- **[前X秒：起始画面与动态描述]**：将第一个图片提示词（场景A）的内容动态化。读取第一个提示词中的静态元素，为其添加一个微小但合理的初始动作，使其"活起来"。
- **[后Y秒：核心互动与过渡描述]**：演绎"核心变化"的过程，将视频从场景A的状态平滑过渡到场景B的状态。描述主动方如何行动，被动方如何反应，最终画面定格在第二个图片提示词（场景B）所描述的场景上。
- **[整体情感与氛围描述]**：为整个视频片段设定统一或递进的情感基调。结合两个提示词中的情绪关键词，提炼出整体的氛围。
- **[技术性指令]**：对AI进行技术性约束，保证成片质量。

**Shot1 图片提示词：** ${shot1Prompt}
**Shot2 图片提示词：** ${shot2Prompt}

请根据以上两个图片提示词，按照首尾帧模板生成动态视频提示词。`
      } else {
        // 没有下一个Shot，使用单帧模式
        console.log("⚠️ 首尾帧模式：没有找到下一个Shot，使用单帧模式")
        adjustedVideoPrompt = `首帧模式：${videoPrompt}。基于单张图片创建动态视频内容。`
      }
    } else {
      // 首帧模式：单图片激活视频提示词
      adjustedVideoPrompt = `首帧模式：${videoPrompt}。基于单张图片创建动态视频内容。`
    }

    setGeneratingVideos(prev => new Set(prev).add(shotId))
    
    try {
      console.log("🎬 开始生成视频，分镜ID:", shotId)
      console.log("🎨 使用视频模型:", videoModel)
      console.log("🎭 分析模式:", analysisMode)
      console.log("🖼️ 图片URL:", imageUrl)
      console.log("📝 原始视频提示词:", videoPrompt)
      console.log("📝 调整后视频提示词:", adjustedVideoPrompt)
      
      // 获取当前分镜信息（只声明一次）
      const currentShot = shots.find(s => s.id === shotId)
      
      // 显示当前分镜的图片尺寸信息
      if (currentShot?.image_size) {
        console.log("📐 图片尺寸:", currentShot.image_size)
      }

      // 优先使用原始图片URL（公网可访问），如果没有则使用本地URL
      let videoImageUrl = imageUrl
      
      // 查找当前分镜的原始图片URL
      if (currentShot?.original_image_url && imageUrl.startsWith('/')) {
        videoImageUrl = currentShot.original_image_url
        console.log("🔄 使用原始图片URL（公网可访问）:", videoImageUrl)
      } else {
        console.log("🔄 使用当前图片URL:", videoImageUrl)
      }

      // 根据选择的模型调用不同的API
      let response, data, requestIdField, statusEndpoint

      if (videoModel === "RUNWAY") {
        // 根据图片尺寸确定视频比例
        let videoRatio = "16:9" // 默认比例
        
        if (currentShot?.image_size) {
          // 根据图片尺寸映射到视频比例
          // RUNWAY API可能支持的比例格式（需要根据实际API文档调整）
          const ratioMapping: Record<string, string> = {
            "1:1": "1:1",
            "2:3": "2:3", 
            "3:2": "3:2",
            "9:16": "9:16", // 竖屏格式
            "16:9": "16:9", // 横屏格式
            // 支持演示数据中的格式
            "portrait-2-3": "2:3",
            "landscape-3-2": "3:2",
            "square-1-1": "1:1",
            "portrait-9-16": "9:16", // 竖屏格式
            "landscape-16-9": "16:9", // 横屏格式
          }
          videoRatio = ratioMapping[currentShot.image_size] || "16:9"
          console.log("📐 根据图片尺寸设置视频比例:", currentShot.image_size, "->", videoRatio)
          
          // 如果RUNWAY API不支持某些比例，可以在这里添加映射
          // 例如：如果RUNWAY只支持16:9和9:16，可以强制映射
          const supportedRatios = ["16:9", "9:16", "1:1"] // 根据实际API支持情况调整
          if (!supportedRatios.includes(videoRatio)) {
            console.log("⚠️ RUNWAY API可能不支持比例:", videoRatio, "，使用默认比例16:9")
            videoRatio = "16:9"
          }
        }
        
        // RUNWAY API调用
        response = await fetch("/api/runway/video", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            promptImage: videoImageUrl, // 添加图片URL
            promptText: adjustedVideoPrompt,
            model: "gen3",
            duration: 10,
            ratio: videoRatio, // 使用动态比例
            style: "cinematic"
          }),
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || "RUNWAY视频生成请求失败")
        }

        data = await response.json()
        requestIdField = "task_id"
        statusEndpoint = "/api/runway/status"
        
        if (!data.task_id) {
          throw new Error("RUNWAY API返回数据格式错误：缺少task_id")
        }
      } else {
        // WAN API调用（默认）
        response = await fetch("/api/wan/video", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            image_url: videoImageUrl,
            prompt: adjustedVideoPrompt
          }),
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || "WAN视频生成请求失败")
        }

        data = await response.json()
        requestIdField = "request_id"
        statusEndpoint = "/api/wan/status"
        
        if (!data.request_id) {
          throw new Error("WAN API返回数据格式错误：缺少request_id")
        }
      }

      console.log(`✅ ${videoModel}视频生成请求成功:`, data)
      const requestId = data[requestIdField]

      // 第二步：轮询检查生成状态（长队列优化版本）
      let pollCount = 0
      let retryCount = 0
      let maxPolls = 60 // 基础轮询次数
      const maxRetries = 3
      let lastQueuePosition = 999 // 记录上次队列位置
      
      const pollStatus = async (): Promise<any> => {
        pollCount++
        console.log(`🔄 轮询状态检查 ${pollCount}/${maxPolls}`)
        
        if (pollCount > maxPolls) {
          const queuePos = lastQueuePosition
          if (queuePos > 50) {
            throw new Error(`队列太长（位置${queuePos}），预计等待时间过长。建议稍后重试或选择非高峰时段。`)
          } else {
            throw new Error(`视频生成超时（已等待${Math.round(pollCount * 12 / 60)}分钟），请稍后重试。`)
          }
        }

        try {
          // 添加超时控制
          const controller = new AbortController()
          const timeoutId = setTimeout(() => {
            console.log('⏰ 请求超时，中止请求')
            controller.abort()
          }, 12000) // 12秒超时

          const statusUrl = videoModel === "RUNWAY" 
            ? `${statusEndpoint}?task_id=${requestId}`
            : `${statusEndpoint}?request_id=${requestId}`
          
          const statusResponse = await fetch(statusUrl, {
            signal: controller.signal
          })
          
          clearTimeout(timeoutId)
          
          if (!statusResponse.ok) {
            throw new Error(`状态检查失败: HTTP ${statusResponse.status}`)
          }

          const statusData = await statusResponse.json()
          const currentQueuePosition = statusData.queue_position || 0
          
          // 动态调整最大轮询次数
          if (currentQueuePosition > 80) {
            maxPolls = Math.max(maxPolls, 120) // 队列很长时增加到120次 (约20-30分钟)
            console.log("📊 状态检查结果:", statusData.status, `队列位置: ${currentQueuePosition} (队列较长，已延长等待时间)`)
          } else if (currentQueuePosition > 30) {
            maxPolls = Math.max(maxPolls, 90) // 队列中等时增加到90次 (约15-20分钟)
            console.log("📊 状态检查结果:", statusData.status, `队列位置: ${currentQueuePosition} (队列中等，已适当延长等待时间)`)
          } else {
            console.log("📊 状态检查结果:", statusData.status, `队列位置: ${currentQueuePosition}`)
          }
          
          // 更新队列位置记录
          lastQueuePosition = currentQueuePosition
          
          // 重置重试计数器（成功请求）
          retryCount = 0
          
          if (statusData.status === "COMPLETED" || statusData.status === "succeeded" || (videoModel === "RUNWAY" && statusData.status === "succeeded")) {
            console.log('🎉 视频生成完成，获取最终结果...')
            
            if (videoModel === "RUNWAY") {
              // RUNWAY模型直接返回statusData，里面包含了视频URL
              console.log("🎯 RUNWAY视频生成完成:", statusData)
              return statusData
            } else {
              // WAN模型需要额外请求获取结果
              const resultController = new AbortController()
              const resultTimeoutId = setTimeout(() => resultController.abort(), 15000) // 15秒超时
              
              const resultResponse = await fetch("/api/wan/status", {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify({
                  request_id: requestId
                }),
                signal: resultController.signal
              })
              
              clearTimeout(resultTimeoutId)
              
              if (!resultResponse.ok) {
                throw new Error(`获取结果失败: HTTP ${resultResponse.status}`)
              }
              
              const resultData = await resultResponse.json()
              console.log("🎯 WAN视频生成完成:", resultData)
              return resultData
            }
          } else if (statusData.status === "FAILED" || statusData.status === "failed") {
            throw new Error(`视频生成失败: ${statusData.error || statusData.failure_reason || '服务器端处理失败'}`)
          } else if (statusData.status === "IN_QUEUE" || statusData.status === "IN_PROGRESS" || 
                     (videoModel === "RUNWAY" && (statusData.status === "pending" || statusData.status === "running" || statusData.status === "processing"))) {
            // 根据队列位置动态调整等待时间
            let waitTime;
            if (currentQueuePosition > 50) {
              // 队列很长时，等待时间更长
              waitTime = Math.min(15000 + (pollCount * 500), 25000) // 15-25秒
              console.log(`⏱️ 队列较长，等待 ${waitTime/1000} 秒后继续检查...`)
            } else if (currentQueuePosition > 20) {
              // 队列中等时
              waitTime = Math.min(10000 + (pollCount * 800), 18000) // 10-18秒
              console.log(`⏱️ 队列中等，等待 ${waitTime/1000} 秒后继续检查...`)
            } else {
              // 队列较短时
              waitTime = Math.min(6000 + (pollCount * 1000), 12000) // 6-12秒
              console.log(`⏱️ 快到了，等待 ${waitTime/1000} 秒后继续检查...`)
            }
            
            // 估算剩余等待时间并更新UI
            if (currentQueuePosition > 5) {
              const estimatedMinutes = Math.round(currentQueuePosition * 0.5) // 假设每个任务30秒
              console.log(`🕒 预计还需等待约 ${estimatedMinutes} 分钟`)
              
              // 更新UI显示
              setVideoQueueInfo(prev => new Map(prev.set(shotId, {
                position: currentQueuePosition,
                estimated: estimatedMinutes > 60 ? `${Math.round(estimatedMinutes/60)}小时` : `${estimatedMinutes}分钟`
              })))
            } else {
              // 快到了，移除队列信息
              setVideoQueueInfo(prev => {
                const newMap = new Map(prev)
                newMap.delete(shotId)
                return newMap
              })
            }
            
            await new Promise(resolve => setTimeout(resolve, waitTime))
            return pollStatus()
          } else {
            throw new Error(`未知状态: ${statusData.status}`)
          }
          
        } catch (error: any) {
          retryCount++
          console.error(`❌ 状态检查失败 (重试 ${retryCount}/${maxRetries}):`, error.message)
          
          // 如果是超时或网络错误，尝试重试
          if ((error.name === 'AbortError' || error.message.includes('fetch failed') || error.message.includes('SocketError')) && retryCount <= maxRetries) {
            const retryDelay = 8000 + (retryCount * 2000) // 8, 10, 12秒递增
            console.log(`🔄 网络错误，${retryDelay/1000} 秒后重试...`)
            await new Promise(resolve => setTimeout(resolve, retryDelay))
            pollCount-- // 重试时不增加轮询计数
            return pollStatus()
          } else {
            // 其他错误或重试次数用完，直接抛出
            throw error
          }
        }
      }

      const result = await pollStatus()
      
      // 第三步：处理视频生成结果
      if (videoModel === "RUNWAY") {
        // RUNWAY模型的特殊处理
        if (result?.has_real_video === false && result?.download_available === true) {
          // RUNWAY API没有真实视频，但有下载选项
          try {
            await db.updateShot(shotId, {
              status: "completed",
              runway_task_id: result.task_id // 保存task_id用于后续下载
            })
            console.log("💾 RUNWAY任务ID已保存到数据库")
            
            // 重新加载分镜数据以确保同步
            await loadShots()
            
            toast({
              title: "视频生成成功",
              description: `分镜 ${shotId} 的视频已生成，请手动下载`,
            })
          } catch (dbError) {
            console.error("保存RUNWAY任务ID到数据库失败:", dbError)
            toast({
              title: "警告",
              description: "视频生成成功，但保存到数据库失败",
              variant: "destructive",
            })
          }
        } else if (result?.video_url) {
          // 有真实视频URL的情况
          try {
            await db.updateShot(shotId, {
              video_url: result.video_url,
              status: "completed"
            })
            console.log("💾 视频URL已保存到数据库")
            
            // 重新加载分镜数据以确保同步
            await loadShots()
            
            toast({
              title: "视频生成成功",
              description: `分镜 ${shotId} 的视频已生成并保存`,
            })
          } catch (dbError) {
            console.error("保存视频URL到数据库失败:", dbError)
            toast({
              title: "警告",
              description: "视频生成成功，但保存到数据库失败",
              variant: "destructive",
            })
          }
        } else {
          throw new Error("RUNWAY API返回数据格式错误：无法获取视频信息")
        }
      } else {
        // WAN模型的返回格式  
        const videoUrl = result?.video_url || result?.video?.url
        
        if (result && videoUrl) {
          try {
            await db.updateShot(shotId, {
              video_url: videoUrl,
              status: "completed"
            })
            console.log("💾 视频URL已保存到数据库")
            
            // 重新加载分镜数据以确保同步
            await loadShots()
            
            toast({
              title: "视频生成成功",
              description: `分镜 ${shotId} 的视频已生成并保存`,
            })
          } catch (dbError) {
            console.error("保存视频URL到数据库失败:", dbError)
            toast({
              title: "警告",
              description: "视频生成成功，但保存到数据库失败",
              variant: "destructive",
            })
          }
        } else {
          throw new Error("API返回数据格式错误：缺少video_url字段")
        }
      }
      
    } catch (error) {
      console.error("生成视频失败:", error)
      toast({
        title: "视频生成失败",
        description: error instanceof Error ? error.message : "未知错误",
        variant: "destructive",
      })
    } finally {
      setGeneratingVideos(prev => {
        const newSet = new Set(prev)
        newSet.delete(shotId)
        return newSet
      })
      // 清除队列信息
      setVideoQueueInfo(prev => {
        const newMap = new Map(prev)
        newMap.delete(shotId)
        return newMap
      })
    }
  }

  // 生成单个分镜的图片
  const generateImage = async (shotId: string, imagePrompt: string) => {
    if (!imagePrompt.trim()) {
      toast({
        title: "错误",
        description: "图片提示词不能为空",
        variant: "destructive",
      })
      return
    }

    setGeneratingImages(prev => new Set(prev).add(shotId))
    
    try {
      console.log("🎨 开始生成图片，分镜ID:", shotId)
      console.log("🖼️ 图片提示词:", imagePrompt)
      console.log("🤖 使用模型:", generationModel)

      // 根据选择的模型确定API端点
      let apiEndpoint
      switch (generationModel) {
        case "doubao":
          apiEndpoint = "/api/doubao"
          break
        case "gpt":
          apiEndpoint = "/api/gpt"
          break
        case "schnell":
          apiEndpoint = "/api/schnell"
          break
        case "sora":
          apiEndpoint = "/api/sora/image"
          break
        default:
          apiEndpoint = "/api/flux"
      }
      
      // 根据不同的API构建不同的请求体
      let requestBody
      if (generationModel === "sora") {
        // SORA API使用不同的请求格式
        requestBody = {
          prompt: imagePrompt,
          model: "gpt-image-1",
          size: selectedImageSize,
          n: 1
        }
      } else {
        // 其他API使用标准格式
        requestBody = {
          prompt: imagePrompt,
          size: selectedImageSize
        }
      }
      
      const response = await fetch(apiEndpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "图片生成失败")
      }

      const data = await response.json()
      
      // 根据不同的API处理不同的响应格式
      let imageUrl
      if (generationModel === "sora") {
        // SORA API响应格式
        imageUrl = data.image_url || data.data?.[0]?.url
      } else {
        // 其他API响应格式
        imageUrl = data.data?.[0]?.url || data.url
      }
      
      if (imageUrl) {
        console.log("✅ 图片生成成功:", imageUrl)
        
        // 先存储图片到本地
        try {
          const storeResponse = await fetch('/api/store-image', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ 
              imageUrl: imageUrl, 
              shotId: shotId 
            })
          })
          
          if (storeResponse.ok) {
            const storeData = await storeResponse.json()
            const localImageUrl = storeData.localUrl
            
            console.log("🏠 图片已存储到本地:", localImageUrl)
            
            // 保存本地图片URL到数据库
            try {
              await db.updateShot(shotId, {
                image_url: localImageUrl,
                original_image_url: imageUrl, // 保存原始URL作为备份
                image_size: selectedImageSize, // 保存图片尺寸
                generation_model: generationModel, // 保存实际使用的模型
                status: "completed"
              })
              console.log("💾 本地图片URL已保存到数据库")
              console.log("🔍 保存的图片信息:", {
                shotId,
                localImageUrl,
                originalUrl: imageUrl,
                imageSize: selectedImageSize
              })
            } catch (dbError) {
              console.error("保存图片URL到数据库失败:", dbError)
              toast({
                title: "警告",
                description: "图片生成成功，但保存到数据库失败",
                variant: "destructive",
              })
            }
            
            // 重新加载分镜数据以确保与数据库同步
            console.log("🔄 重新加载分镜数据以确保同步...")
            await loadShots()
            
          } else {
            console.error("存储图片到本地失败，使用原始URL")
            // 如果存储失败，回退到保存原始URL
            try {
              await db.updateShot(shotId, {
                image_url: imageUrl,
                image_size: selectedImageSize, // 保存图片尺寸
                generation_model: generationModel, // 保存实际使用的模型
                status: "completed"
              })
              console.log("💾 已保存原始图片URL到数据库")
            } catch (dbError) {
              console.error("保存原始图片URL到数据库也失败:", dbError)
              toast({
                title: "警告",
                description: "图片生成成功，但数据库保存失败",
                variant: "destructive",
              })
            }
            
            // 重新加载分镜数据以确保与数据库同步
            console.log("🔄 重新加载分镜数据以确保同步...")
            await loadShots()
          }
        } catch (storeError) {
          console.error("存储图片过程出错:", storeError)
          // 如果存储过程出错，回退到保存原始URL
          try {
            await db.updateShot(shotId, {
              image_url: data.data[0].url,
              image_size: selectedImageSize, // 保存图片尺寸
              generation_model: generationModel, // 保存实际使用的模型
              status: "completed"
            })
            console.log("💾 已保存原始图片URL到数据库(回退方案)")
          } catch (dbError) {
            console.error("保存原始图片URL到数据库也失败:", dbError)
            toast({
              title: "警告",
              description: "图片生成成功，但数据库保存失败",
              variant: "destructive",
            })
          }
          
          // 重新加载分镜数据以确保与数据库同步
          console.log("🔄 重新加载分镜数据以确保同步...")
          await loadShots()
        }
        
        toast({
          title: "图片生成成功",
          description: `分镜 ${shotId} 的图片已生成并保存`,
        })
        
        // 额外的数据同步验证
        console.log("🔍 验证图片是否正确保存...")
        setTimeout(async () => {
          await loadShots()
          console.log("🔄 延迟刷新完成")
        }, 1000)
      } else {
        throw new Error("API返回数据格式错误")
      }
    } catch (error) {
      console.error("生成图片失败:", error)
      toast({
        title: "图片生成失败", 
        description: error instanceof Error ? error.message : "未知错误",
        variant: "destructive",
      })
    } finally {
      setGeneratingImages(prev => {
        const newSet = new Set(prev)
        newSet.delete(shotId)
        return newSet
      })
    }
  }

  const handleGenerateAllImages = async () => {
    if (shots.length === 0) {
      toast({
        title: "提示",
        description: "没有分镜需要生成图片",
        variant: "destructive",
      })
      return
    }

    setIsGenerating(true)
    console.log(`🎬 开始批量生成 ${shots.length} 个分镜的图片`)
    
    try {
      // 逐个生成图片，避免并发请求过多
      let successCount = 0
      let failCount = 0
      
      for (const shot of shots) {
        if (!shot.image_prompt?.trim()) {
          console.log(`⏭️ 跳过分镜 ${shot.shot_number}：没有图片提示词`)
          continue
        }
        
        try {
          console.log(`🎨 正在生成分镜 ${shot.shot_number} 的图片...`)
          await generateImage(shot.id, shot.image_prompt)
          successCount++
          console.log(`✅ 分镜 ${shot.shot_number} 生成成功`)
          
          // 每生成一张图片后等待一下，避免API限制
          await new Promise(resolve => setTimeout(resolve, 1500)) // 增加间隔时间
        } catch (error) {
          console.error(`❌ 分镜 ${shot.shot_number} 生成失败:`, error)
          failCount++
          
          // 可选：失败后额外等待，避免连续失败
          await new Promise(resolve => setTimeout(resolve, 2000))
        }
      }

      toast({
        title: "批量生成完成",
        description: `成功: ${successCount} 个，失败: ${failCount} 个`,
      })

      // 重新加载分镜数据
      await loadShots()
    } catch (error) {
      console.error("❌ 批量生成图片失败:", error)
      toast({
        title: "批量生成失败",
        description: "请检查网络连接或稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsGenerating(false)
    }
  }

  // 批量生成所有视频功能
  const handleGenerateAllVideos = async () => {
    const shotsWithImages = shots.filter(shot => shot.image_url && shot.english_prompt?.trim())
    
    if (shotsWithImages.length === 0) {
      toast({
        title: "提示",
        description: "没有可生成视频的分镜（需要有图片和视频提示词）",
        variant: "destructive",
      })
      return
    }

    setIsGeneratingVideos(true)
    console.log(`🎬 开始批量生成 ${shotsWithImages.length} 个视频`)
    
    try {
      let successCount = 0
      let failCount = 0
      
      for (const shot of shotsWithImages) {
        if (!shot.english_prompt?.trim()) {
          console.log(`⏭️ 跳过分镜 ${shot.shot_number}：没有视频提示词`)
          continue
        }
        
        try {
          console.log(`🎬 正在生成分镜 ${shot.shot_number} 的视频...`)
          await generateVideo(shot.id, shot.image_url!, shot.english_prompt)
          successCount++
          console.log(`✅ 分镜 ${shot.shot_number} 视频生成成功`)
          
          // 每生成一个视频后等待，避免API限制
          await new Promise(resolve => setTimeout(resolve, 2000))
        } catch (error) {
          console.error(`❌ 分镜 ${shot.shot_number} 视频生成失败:`, error)
          failCount++
          
          // 失败后额外等待，避免连续失败
          await new Promise(resolve => setTimeout(resolve, 3000))
        }
      }

      toast({
        title: "批量视频生成完成",
        description: `成功: ${successCount} 个，失败: ${failCount} 个`,
      })

      // 重新加载分镜数据
      await loadShots()
    } catch (error) {
      console.error("❌ 批量生成视频失败:", error)
      toast({
        title: "批量视频生成失败",
        description: "请检查网络连接或稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsGeneratingVideos(false)
    }
  }

  // 批量下载所有图片功能
  const handleDownloadAllImages = async () => {
    const shotsWithImages = shots.filter(shot => shot.image_url)
    
    if (shotsWithImages.length === 0) {
      toast({
        title: "提示",
        description: "没有可下载的图片",
        variant: "destructive",
      })
      return
    }

    setIsDownloading(true)
    console.log(`📥 开始批量下载 ${shotsWithImages.length} 张图片`)
    
    try {
      let successCount = 0
      let failCount = 0
      
      for (const shot of shotsWithImages) {
        try {
          console.log(`📥 正在下载分镜 ${shot.shot_number} 的图片...`)
          await downloadImage(shot.image_url!, shot.shot_number)
          successCount++
          console.log(`✅ 分镜 ${shot.shot_number} 下载成功`)
          
          // 每下载一张图片后等待一下，避免浏览器限制
          await new Promise(resolve => setTimeout(resolve, 500))
        } catch (error) {
          console.error(`❌ 分镜 ${shot.shot_number} 下载失败:`, error)
          failCount++
        }
      }

      toast({
        title: "批量下载完成",
        description: `成功: ${successCount} 个，失败: ${failCount} 个`,
      })

    } catch (error) {
      console.error("❌ 批量下载图片失败:", error)
      toast({
        title: "批量下载失败",
        description: "请检查网络连接或稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsDownloading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-full bg-gray-950 p-8 flex items-center justify-center">
        <div className="flex items-center gap-2 text-white">
          <Loader2 className="w-6 h-6 animate-spin" />
          <span>Loading storyboards...</span>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-full bg-gray-950 p-8 flex items-center justify-center">
        <div className="text-white">Please sign in to access storyboards</div>
      </div>
    )
  }

  if (projects.length === 0) {
    return (
      <div className="min-h-full bg-gray-950 p-8 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-white mb-4">No Projects Found</h2>
          <p className="text-gray-400 mb-6">Create a project first to start working with storyboards</p>
          <Button onClick={() => (window.location.href = "/")}>Go to Dashboard</Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-full bg-gray-950 p-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <h1 className="text-3xl font-bold text-white">Storyboard List</h1>
            <Badge className="bg-green-600 hover:bg-green-700 text-white">
              <CheckCircle className="w-4 h-4 mr-1" />
              {shots.filter((s) => s.status === "completed").length} Images Generated
            </Badge>
          </div>

          {/* Top Controls */}
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-400">Image Size:</span>
              <Select value={selectedImageSize} onValueChange={setSelectedImageSize}>
                <SelectTrigger className="w-44 bg-gray-800 border-gray-700 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-700">
                  {imageSizeOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-400">Generation Model:</span>
              <Select value={generationModel} onValueChange={setGenerationModel}>
                <SelectTrigger className="w-32 bg-gray-800 border-gray-700 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-700">
                  <SelectItem value="schnell">
                    <div className="flex items-center gap-2">
                      <span>Schnell</span>
                      <span className="text-xs text-blue-400">极快</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="flux">
                    <div className="flex items-center gap-2">
                      <span>FLUX</span>
                      <span className="text-xs text-green-400">快速</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="sora">
                    <div className="flex items-center gap-2">
                      <span>SORA</span>
                      <span className="text-xs text-purple-400">高质量</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="doubao">豆包</SelectItem>
                  <SelectItem value="gpt">
                    <div className="flex items-center gap-2">
                      <span>GPT-4o</span>
                      <span className="text-xs text-orange-400">较慢</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
              {generationModel === "gpt" && (
                <span className="text-xs text-orange-400">⚠️ 此模型生成较慢</span>
              )}
              {generationModel === "schnell" && (
                <span className="text-xs text-blue-400">🚀 快速预览模型，适合快速迭代</span>
              )}
              {generationModel === "sora" && (
                <span className="text-xs text-purple-400">🎨 高质量图片生成模型</span>
              )}
            </div>

            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-400">Image Style:</span>
              <Select value={imageStyle} onValueChange={setImageStyle}>
                <SelectTrigger className="w-36 bg-gray-800 border-gray-700 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-700">
                  <SelectItem value="realistic-photography">Realistic Photography</SelectItem>
                  <SelectItem value="anime">Anime</SelectItem>
                  <SelectItem value="oil-painting">Oil Painting</SelectItem>
                  <SelectItem value="watercolor">Watercolor</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-400">Video Model:</span>
              <Select value={videoModel} onValueChange={setVideoModel}>
                <SelectTrigger className="w-32 bg-gray-800 border-gray-700 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-700">
                  <SelectItem value="WAN">
                    <div className="flex items-center gap-2">
                      <span>WAN</span>
                      <span className="text-xs text-green-400">稳定</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="RUNWAY">
                    <div className="flex items-center gap-2">
                      <span>RUNWAY</span>
                      <span className="text-xs text-green-400">可用</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
              {videoModel === "RUNWAY" && (
                <span className="text-xs text-green-400">⚡ 高质量视频生成模型</span>
              )}
              {videoModel === "WAN" && (
                <span className="text-xs text-green-400">⚡ 快速稳定模型</span>
              )}
            </div>


          </div>
        </div>

        {/* Story Input Section */}
        <Card className="glass-card mb-6">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <Sparkles className="w-5 h-5" />
              AI Story Analysis
            </CardTitle>
            <CardDescription className="text-gray-400">
              Write your story or paste an existing script. Our AI will automatically break it down into professional shots.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="story" className="text-white">
                Story Content
              </Label>
            </div>
            
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-400">Video Mode:</span>
                <Select value={analysisMode} onValueChange={(value) => {
                  console.log("🎯 Video Mode 选择器变化:", value)
                  setAnalysisMode(value)
                }}>
                  <SelectTrigger className="w-32 bg-gray-800 border-gray-700 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-800 border-gray-700">
                    <SelectItem value="single-frame">
                      <div className="flex items-center gap-2">
                        <span>首帧</span>
                        <span className="text-xs text-blue-400">单图片</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="dual-frame">
                      <div className="flex items-center gap-2">
                        <span>首尾帧</span>
                        <span className="text-xs text-green-400">双图片</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              {analysisMode === "dual-frame" && (
                <div className="text-xs text-green-400">
                  🎬 首尾帧模式：将两张图片转化为叙事视频
                </div>
              )}
              {analysisMode === "single-frame" && (
                <div className="text-xs text-blue-400">
                  🖼️ 首帧模式：基于单张图片创建动态视频
                </div>
              )}
            </div>
            
            <div className="flex gap-4">
              <Textarea
                id="story"
                placeholder="输入你的故事内容..."
                value={story}
                onChange={(e) => setStory(e.target.value)}
                className="flex-1 bg-gray-800/50 border-gray-700 text-white placeholder:text-gray-500 min-h-[120px] resize-none"
                disabled={isAnalyzing}
              />
              <Button
                onClick={handleAnalyze}
                disabled={!story.trim() || isAnalyzing || !user}
                className="bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700 text-white font-semibold px-6"
              >
                {isAnalyzing ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    分析中...
                  </>
                ) : (
                  <>
                    <Sparkles className="w-4 h-4 mr-2" />
                    开始分镜分析
                  </>
                )}
              </Button>
            </div>
            
            <div className="flex justify-between items-center text-sm text-gray-400">
              <div>
                {story.length > 0 && `${story.length} characters`}
              </div>
            </div>

            {/* Processing Indicator */}
            {isAnalyzing && (
              <Card className="glass-card border-blue-500/50 bg-blue-500/5">
                <CardContent className="p-4">
                  <div className="flex items-center space-x-3">
                    <Loader2 className="w-6 h-6 text-blue-400 animate-spin flex-shrink-0" />
                    <div>
                      <div className="text-sm font-medium text-white">正在分析故事...</div>
                      <div className="text-xs text-gray-400">
                        AI 正在将您的故事拆解为专业分镜，这通常需要 10-30 秒
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </CardContent>
        </Card>

        {/* Modification Instructions */}
        <Card className="glass-card mb-6">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-white flex items-center gap-2">
                <span className="bg-red-500/20 text-red-400 px-2 py-1 rounded text-xs font-semibold">BATCH MODE</span>
                批量修改指令 - 将修改所有镜头
              </CardTitle>
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-400">Generation Mode:</span>
                <Select value={generationMode} onValueChange={setGenerationMode}>
                  <SelectTrigger className="w-32 bg-gray-800 border-gray-700 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-800 border-gray-700">
                    <SelectItem value="smart">Smart Mode</SelectItem>
                    <SelectItem value="manual">Manual Mode</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-4">
              <Textarea
                placeholder='Enter modification instructions, such as: "Change all scenes to night", "Add rain effects", "Adjust character expressions", etc...'
                value={modificationText}
                onChange={(e) => setModificationText(e.target.value)}
                className="flex-1 bg-gray-800/50 border-gray-700 text-white placeholder:text-gray-500 min-h-[100px]"
                disabled={isModifying}
              />
              <div className="flex flex-col gap-2">
                <Button
                  onClick={handleSmartModify}
                  disabled={!modificationText.trim() || isModifying || !user || shots.length === 0}
                  className="bg-red-600 hover:bg-red-700 text-white px-6 border-2 border-red-500"
                >
                  {isModifying ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      批量修改中...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2" />
                      批量修改所有镜头
                    </>
                  )}
                </Button>
                {undoData && (
                  <Button
                    onClick={handleUndo}
                    disabled={isModifying}
                    variant="outline"
                    className="border-green-600 text-green-400 hover:bg-green-600 hover:text-white bg-transparent px-6"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-4 h-4 mr-2">
                      <path d="M3 7v6h6"/>
                      <path d="M21 17a9 9 0 0 0-9-9 9 9 0 0 0-6 2.3L3 13"/>
                    </svg>
                    恢复
                    {undoData.type === 'single' ? '镜头' : '所有镜头'}
                  </Button>
                )}
                {undoData && (
                  <Button
                    onClick={clearUndoData}
                    disabled={isModifying}
                    variant="ghost"
                    size="sm"
                    className="text-gray-400 hover:text-white text-xs"
                  >
                    清除恢复数据
                  </Button>
                )}
              </div>
            </div>
            <div className="flex items-center justify-between text-sm text-gray-400">
              <div className="flex items-center gap-2">
                <span>⚠️</span>
                <span className="text-red-400 font-medium">
                  警告：此功能将修改所有镜头！如需修改单个镜头，请使用镜头卡片中的蓝色"修改此镜头"按钮
                </span>
              </div>
              
              {/* 恢复功能说明 */}
              {!undoData && (
                <div className="flex items-center gap-2 mt-2">
                  <span>💡</span>
                  <span className="text-blue-400 text-sm">
                    修改后，绿色的"恢复"按钮将在此处显示，支持一键撤销修改
                  </span>
                </div>
              )}
              <span>Current shot count: {shots.length}</span>
            </div>

            {/* Modification Processing Indicator */}
            {isModifying && (
              <Card className="glass-card border-orange-500/50 bg-orange-500/5 mt-4">
                <CardContent className="p-4">
                  <div className="flex items-center space-x-3">
                    <Loader2 className="w-6 h-6 text-orange-400 animate-spin flex-shrink-0" />
                    <div>
                      <div className="text-sm font-medium text-white">正在批量修改分镜...</div>
                      <div className="text-xs text-gray-400">
                        AI 正在根据您的指令修改所有分镜，这通常需要 20-40 秒
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </CardContent>
        </Card>

        {/* Project Controls */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <span className="text-white">Project Name:</span>
              {editingProject ? (
                <div className="flex items-center gap-2">
                  <input
                    type="text"
                    value={editValues.title || ''}
                    onChange={(e) => setEditValues({...editValues, title: e.target.value})}
                    className="w-40 bg-gray-800 border border-gray-700 text-white px-3 py-2 rounded-md"
                    placeholder="Project title"
                  />
                  <Button 
                    onClick={handleProjectSave}
                    className="bg-green-600 hover:bg-green-700 text-white px-3 py-1"
                  >
                    Save
                  </Button>
                  <Button 
                    onClick={handleProjectCancel}
                    variant="outline"
                    className="border-gray-700 text-white hover:bg-gray-800 px-3 py-1"
                  >
                    Cancel
                  </Button>
                </div>
              ) : (
                <Select value={selectedProjectId} onValueChange={(value) => {
                  setSelectedProjectId(value)
                  // 保存到本地存储
                  localStorage.setItem('last-selected-project', value)
                  console.log("💾 保存项目选择到本地存储:", value)
                }}>
                  <SelectTrigger className="w-40 bg-gray-800 border-gray-700 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-800 border-gray-700">
                    {projects.map((project) => (
                      <SelectItem key={project.id} value={project.id}>
                        {project.title}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            </div>
            {!editingProject && (
              <>
                <Button 
                  variant="outline" 
                  className="border-gray-700 text-white hover:bg-gray-800 bg-transparent"
                  onClick={handleProjectEdit}
                >
                  <Edit className="w-4 h-4 mr-2" />
                  Edit
                </Button>
                
                {/* 删除项目按钮 */}
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button 
                      variant="outline" 
                      className="border-red-600 text-red-400 hover:bg-red-600/10 bg-transparent"
                      disabled={!selectedProjectId || projects.length === 0}
                    >
                      <Trash2 className="w-4 h-4 mr-2" />
                      Delete
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent className="bg-gray-900 border-gray-700">
                    <AlertDialogHeader>
                      <AlertDialogTitle className="text-white">Delete Project</AlertDialogTitle>
                      <AlertDialogDescription className="text-gray-400">
                        Are you sure you want to delete this project? This action cannot be undone.
                        All shots and data associated with this project will be permanently removed.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel className="border-gray-700 text-white hover:bg-gray-800">
                        Cancel
                      </AlertDialogCancel>
                      <AlertDialogAction 
                        onClick={handleProjectDelete}
                        disabled={isDeleting}
                        className="bg-red-600 hover:bg-red-700 text-white"
                      >
                        {isDeleting ? (
                          <>
                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                            Deleting...
                          </>
                        ) : (
                          'Delete Project'
                        )}
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </>
            )}
            
            {/* 刷新分镜数据按钮 */}
            <Button 
              variant="outline" 
              className="border-blue-600 text-blue-400 hover:bg-blue-600/10 bg-transparent"
              onClick={() => {
                console.log("🔄 手动刷新分镜数据")
                loadShots()
              }}
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              刷新分镜
            </Button>
            
            {/* 临时调试按钮 */}
            <Button 
              variant="outline" 
              className="border-yellow-600 text-yellow-400 hover:bg-yellow-600/10 bg-transparent"
              onClick={() => {
                console.log("🔍 当前状态调试:")
                console.log("选中的项目ID:", selectedProjectId)
                console.log("所有项目:", projects)
                console.log("URL参数:", searchParams.get('project'))
                console.log("当前分镜数据:", shots)
                const projectIdFromUrl = searchParams.get('project') || undefined
                loadProjects(projectIdFromUrl)
              }}
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              🐛 调试
            </Button>
            
            <Button className="bg-purple-600 hover:bg-purple-700 text-white">
              <Users className="w-4 h-4 mr-2" />
              Character Management (0)
            </Button>
            <Button onClick={addShot} className="bg-blue-600 hover:bg-blue-700 text-white">
              <Plus className="w-4 h-4 mr-2" />
              Add Shot
            </Button>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-4 mb-8">
          <Button variant="outline" className="border-gray-700 text-white hover:bg-gray-800 bg-transparent">
            <RefreshCw className="w-4 h-4 mr-2" />
            Copy Image Prompts
          </Button>
          <Button
            onClick={handleGenerateAllImages}
            disabled={isGenerating || shots.length === 0}
            className="bg-gray-700 hover:bg-gray-600 text-white"
          >
            {isGenerating ? <Loader2 className="w-4 h-4 mr-2 animate-spin" /> : <ImageIcon className="w-4 h-4 mr-2" />}
            {isGenerating ? "Generating..." : "Generate All Images"}
          </Button>
          <Button 
            onClick={handleDownloadAllImages}
            disabled={isDownloading || shots.filter(s => s.image_url).length === 0}
            variant="outline" 
            className="border-gray-700 text-white hover:bg-gray-800 bg-transparent"
          >
            {isDownloading ? <Loader2 className="w-4 h-4 mr-2 animate-spin" /> : <Download className="w-4 h-4 mr-2" />}
            {isDownloading ? "Downloading..." : "Download Images"}
          </Button>
          <Button 
            onClick={handleGenerateAllVideos}
            disabled={isGeneratingVideos || shots.filter(s => s.image_url).length === 0}
            className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white font-semibold border-2 border-purple-500"
          >
            {isGeneratingVideos ? <Loader2 className="w-4 h-4 mr-2 animate-spin" /> : <Video className="w-4 h-4 mr-2" />}
            {isGeneratingVideos ? "批量生成视频中..." : "🎬 批量生成视频"}
          </Button>
          <Button variant="outline" className="border-red-600 text-red-400 hover:bg-red-600/10 bg-transparent">
            <Trash2 className="w-4 h-4 mr-2" />
            Clear Cache
          </Button>
        </div>

        {/* Storyboard Content */}
        <div className="space-y-6">
          {shots.length === 0 ? (
            <Card className="glass-card text-center py-12">
              <CardContent>
                <ImageIcon className="w-16 h-16 mx-auto text-gray-500 mb-4" />
                <h3 className="text-lg font-medium text-white mb-2">No shots in this project</h3>
                <p className="text-gray-400 mb-6">Add your first shot to start creating your storyboard</p>
                <Button onClick={addShot} className="bg-blue-600 hover:bg-blue-700 text-white">
                  <Plus className="w-4 h-4 mr-2" />
                  Add First Shot
                </Button>
              </CardContent>
            </Card>
          ) : (
            shots.map((shot, index) => (
              <Card key={`${shot.id}-${shot.shot_number}-${index}`} className="glass-card">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <span className="text-white">Shot: {shot.shot_number}</span>
                        <Button
                          size="sm"
                          onClick={addShot}
                          className="w-8 h-8 p-0 bg-green-600 hover:bg-green-700 text-white"
                        >
                          <Plus className="w-4 h-4" />
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => removeShot(shot.id)}
                          className="w-8 h-8 p-0 bg-red-600 hover:bg-red-700 text-white"
                        >
                          <Minus className="w-4 h-4" />
                        </Button>
                      </div>
                      <span className="text-gray-400">Project: {selectedProject?.title}</span>
                      <Badge
                        variant={shot.status === "completed" ? "default" : "secondary"}
                        className={shot.status === "completed" ? "bg-green-600" : ""}
                      >
                        {shot.status}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="text-center">
                        <div className="text-sm text-gray-400">图片</div>
                        <div className="text-sm text-gray-500 flex items-center gap-1">
                          {shot.image_url ? (
                            <>
                              <CheckCircle className="w-3 h-3 text-green-500" />
                              已生成
                            </>
                          ) : (
                            "未生成"
                          )}
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-sm text-gray-400">视频</div>
                        <div className="text-sm text-gray-500 flex items-center gap-1">
                          {shot.video_url ? (
                            <>
                              <CheckCircle className="w-3 h-3 text-purple-500" />
                              已生成
                            </>
                          ) : generatingVideos.has(shot.id) ? (
                            <>
                              <Loader2 className="w-3 h-3 animate-spin text-purple-500" />
                              生成中
                            </>
                          ) : (
                            "未生成"
                          )}
                        </div>
                      </div>
                      <Button 
                        variant="outline" 
                        className="border-gray-700 text-white hover:bg-gray-800 bg-transparent"
                        onClick={() => handleShotEdit(shot.id, 'content', shot.content || '')}
                      >
                        <Edit className="w-4 h-4 mr-2" />
                        Edit
                      </Button>
                      <Button
                        variant="outline"
                        className="border-blue-600 text-blue-400 hover:bg-blue-600 hover:text-white bg-transparent"
                        onClick={() => openSmartModifyDialog(shot.id)}
                        disabled={isModifying}
                      >
                        <Sparkles className="w-4 h-4 mr-2" />
                        修改此镜头
                      </Button>
                      {undoData && undoData.type === 'single' && (undoData.data as Shot).id === shot.id && (
                        <Button
                          variant="outline"
                          className="border-green-600 text-green-400 hover:bg-green-600 hover:text-white bg-transparent"
                          onClick={handleUndo}
                          disabled={isModifying}
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-4 h-4 mr-2">
                            <path d="M3 7v6h6"/>
                            <path d="M21 17a9 9 0 0 0-9-9 9 9 0 0 0-6 2.3L3 13"/>
                          </svg>
                          恢复
                        </Button>
                      )}
                      {/* 恢复功能预览提示 - 仅在没有恢复数据时显示 */}
                      {!undoData && (
                        <div className="text-xs text-blue-400 opacity-60">
                          ⬅️ 修改后此处显示恢复按钮
                        </div>
                      )}
                      {/* 图片生成按钮 */}
                      <Button
                        onClick={() => generateImage(shot.id, shot.image_prompt || "")}
                        disabled={generatingImages.has(shot.id) || shot.status === "generating"}
                        className="bg-blue-600 hover:bg-blue-700 text-white"
                        size="sm"
                      >
                        {generatingImages.has(shot.id) ? (
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        ) : (
                          <ImageIcon className="w-4 h-4 mr-2" />
                        )}
                        {generatingImages.has(shot.id) ? "生成中..." : "生成图片"}
                      </Button>
                      
                      {/* 视频生成按钮 */}
                      <div className="relative">
                        <Button
                          onClick={() => generateVideo(shot.id, shot.image_url || "", shot.english_prompt || "")}
                          disabled={generatingVideos.has(shot.id) || !shot.image_url || !shot.english_prompt?.trim()}
                          className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white font-semibold border-2 border-purple-500"
                          size="sm"
                        >
                          {generatingVideos.has(shot.id) ? (
                            <>
                              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                              {videoQueueInfo.has(shot.id) ? (
                                <span className="text-xs">
                                  排队中(#{videoQueueInfo.get(shot.id)?.position}) 
                                  <br />约{videoQueueInfo.get(shot.id)?.estimated}
                                </span>
                              ) : (
                                "生成视频中..."
                              )}
                            </>
                          ) : (
                            <>
                              <Video className="w-4 h-4 mr-2" />
                              🎬 图生视频
                            </>
                          )}
                        </Button>
                        
                        {/* 提示信息 */}
                        {(!shot.image_url || !shot.english_prompt?.trim()) && (
                          <div className="absolute -bottom-6 left-0 text-xs text-gray-400">
                            {!shot.image_url && "需要先生成图片"}
                            {shot.image_url && !shot.english_prompt?.trim() && "需要视频提示词"}
                          </div>
                        )}
                        
                        {/* 成功状态指示 */}
                        {shot.video_url && (
                          <div className="absolute -top-2 -right-2">
                            <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* 左侧：文本内容 */}
                    <div className="space-y-6">
                      {/* Shot Content */}
                      <div>
                        <h4 className="text-white font-medium mb-2">Shot Content</h4>
                        {editingShotId === shot.id && editingField === 'content' ? (
                          <div className="space-y-2">
                            <Textarea
                              value={editValues.content || ''}
                              onChange={(e) => setEditValues({...editValues, content: e.target.value})}
                              className="bg-gray-800/50 border-gray-700 text-white min-h-[100px]"
                            />
                            <div className="flex gap-2">
                              <Button 
                                onClick={handleShotSave}
                                className="bg-green-600 hover:bg-green-700 text-white px-3 py-1"
                              >
                                Save
                              </Button>
                              <Button 
                                onClick={handleShotCancel}
                                variant="outline"
                                className="border-gray-700 text-white hover:bg-gray-800 px-3 py-1"
                              >
                                Cancel
                              </Button>
                            </div>
                          </div>
                        ) : (
                          <p className="text-gray-300 text-sm">{shot.content}</p>
                        )}
                      </div>

                      {/* Image Prompt */}
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="text-white font-medium">Image Prompt</h4>
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            className="text-gray-400 hover:text-white"
                            onClick={() => handleShotEdit(shot.id, 'image_prompt', shot.image_prompt || '')}
                          >
                            <Edit className="w-4 h-4 mr-1" />
                            Edit
                          </Button>
                        </div>
                        {editingShotId === shot.id && editingField === 'image_prompt' ? (
                          <div className="space-y-2">
                            <Textarea
                              value={editValues.image_prompt || ''}
                              onChange={(e) => setEditValues({...editValues, image_prompt: e.target.value})}
                              className="bg-gray-800/50 border-gray-700 text-white min-h-[100px]"
                            />
                            <div className="flex gap-2">
                              <Button 
                                onClick={handleShotSave}
                                className="bg-green-600 hover:bg-green-700 text-white px-3 py-1"
                              >
                                Save
                              </Button>
                              <Button 
                                onClick={handleShotCancel}
                                variant="outline"
                                className="border-gray-700 text-white hover:bg-gray-800 px-3 py-1"
                              >
                                Cancel
                              </Button>
                            </div>
                          </div>
                        ) : (
                          <div className="bg-gray-800/50 p-4 rounded-lg">
                            <ColoredImagePrompt text={shot.image_prompt || ''} />
                          </div>
                        )}
                      </div>

                      {/* Video Prompt */}
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="text-white font-medium">Video Prompt</h4>
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            className="text-gray-400 hover:text-white"
                            onClick={() => handleShotEdit(shot.id, 'english_prompt', shot.english_prompt || '')}
                          >
                            <Edit className="w-4 h-4 mr-1" />
                            Edit
                          </Button>
                        </div>
                        {editingShotId === shot.id && editingField === 'english_prompt' ? (
                          <div className="space-y-2">
                            <Textarea
                              value={editValues.english_prompt || ''}
                              onChange={(e) => setEditValues({...editValues, english_prompt: e.target.value})}
                              className="bg-gray-800/50 border-gray-700 text-white min-h-[100px]"
                            />
                            <div className="flex gap-2">
                              <Button 
                                onClick={handleShotSave}
                                className="bg-green-600 hover:bg-green-700 text-white px-3 py-1"
                              >
                                Save
                              </Button>
                              <Button 
                                onClick={handleShotCancel}
                                variant="outline"
                                className="border-gray-700 text-white hover:bg-gray-800 px-3 py-1"
                              >
                                Cancel
                              </Button>
                            </div>
                          </div>
                        ) : (
                          <div className="bg-gray-800/50 p-4 rounded-lg">
                            <p className="text-gray-300 text-sm leading-relaxed">{shot.english_prompt}</p>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* 右侧：图片和视频预览 */}
                    <div className="space-y-4">
                      {/* 图片预览 */}
                      {shot.image_url ? (
                        <div>
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="text-white font-medium">Image Preview</h4>
                            <Button
                              onClick={() => downloadImage(shot.image_url!, shot.shot_number)}
                              variant="outline"
                              className="border-green-600 text-green-400 hover:bg-green-600/10 bg-transparent"
                            >
                              <Download className="w-4 h-4 mr-2" />
                              Download
                            </Button>
                          </div>
                          <div className="relative flex justify-center">
                            <div className={`${getImagePreviewStyle(shot.image_size || selectedImageSize).aspectRatio} max-w-sm`}>
                              <img
                                src={shot.image_url}
                                alt={`Shot ${shot.shot_number}`}
                                className="w-full h-full object-cover rounded-lg bg-gray-800 border border-gray-700"
                              />
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className={`flex items-center justify-center bg-gray-800/50 border-2 border-dashed border-gray-700 rounded-lg ${getImagePreviewStyle(shot.image_size || selectedImageSize).aspectRatio} max-w-sm mx-auto`}>
                          <div className="text-center">
                            <ImageIcon className="w-16 h-16 mx-auto text-gray-500 mb-4" />
                            <p className="text-gray-400">No image generated yet</p>
                            <p className="text-gray-500 text-sm">Click "Generate Image" to create one</p>
                          </div>
                        </div>
                      )}

                      {/* 视频预览 */}
                      {shot.video_url ? (
                        <div>
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="text-white font-medium">Video Preview</h4>
                            <div className="flex gap-2">
                              <Button
                                onClick={() => downloadVideo(shot.video_url!, shot.shot_number)}
                                variant="outline"
                                className="border-green-600 text-green-400 hover:bg-green-600/10 bg-transparent"
                              >
                                <Download className="w-4 h-4 mr-2" />
                                Download
                              </Button>
                              <Button
                                onClick={() => window.open(shot.video_url!, '_blank')}
                                variant="outline"
                                className="border-purple-600 text-purple-400 hover:bg-purple-600/10 bg-transparent"
                              >
                                <Eye className="w-4 h-4 mr-2" />
                                View
                              </Button>
                            </div>
                          </div>
                          <div className="relative flex justify-center">
                            <div className={`${getImagePreviewStyle(shot.image_size || selectedImageSize).aspectRatio} max-w-sm`}>
                              <video
                                src={shot.video_url}
                                controls
                                loop
                                muted
                                className="w-full h-full object-cover rounded-lg bg-gray-800 border border-gray-700"
                                poster={shot.image_url}
                              >
                                Your browser does not support the video tag.
                              </video>
                            </div>
                          </div>
                        </div>
                      ) : shot.runway_task_id ? (
                        <div>
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="text-white font-medium">RUNWAY Video Generated</h4>
                            <div className="flex gap-2">
                              <Button
                                onClick={() => downloadRunwayVideo(shot.runway_task_id!, shot.shot_number)}
                                variant="outline"
                                className="border-orange-600 text-orange-400 hover:bg-orange-600/10 bg-transparent"
                              >
                                <Download className="w-4 h-4 mr-2" />
                                Download RUNWAY Video
                              </Button>
                            </div>
                          </div>
                          <div className="relative flex justify-center">
                            <div className={`${getImagePreviewStyle(shot.image_size || selectedImageSize).aspectRatio} max-w-sm bg-gray-800 border border-gray-700 rounded-lg flex items-center justify-center`}>
                              <div className="text-center p-6">
                                <Video className="w-16 h-16 mx-auto text-orange-500 mb-4" />
                                <p className="text-orange-400 font-medium mb-2">RUNWAY Video Ready</p>
                                <p className="text-gray-400 text-sm">Video has been generated successfully</p>
                                <p className="text-gray-500 text-xs mt-2">Click "Download RUNWAY Video" to get the file</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      ) : shot.image_url ? (
                        <div className={`flex items-center justify-center bg-gray-800/50 border-2 border-dashed border-purple-700 rounded-lg ${getImagePreviewStyle(shot.image_size || selectedImageSize).aspectRatio} max-w-sm mx-auto p-6`}>
                          <div className="text-center">
                            <Video className="w-16 h-16 mx-auto text-purple-500 mb-4" />
                            <p className="text-purple-400 font-medium mb-2">准备生成视频</p>
                            <p className="text-gray-500 text-sm mb-4">图片已就绪，可以生成视频了</p>
                            
                            <Button
                              onClick={() => generateVideo(shot.id, shot.image_url || "", shot.english_prompt || "")}
                              disabled={generatingVideos.has(shot.id) || !shot.english_prompt?.trim()}
                              className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white font-semibold px-6 py-2"
                            >
                              {generatingVideos.has(shot.id) ? (
                                <div className="text-center">
                                  <Loader2 className="w-5 h-5 mx-auto mb-1 animate-spin" />
                                  {videoQueueInfo.has(shot.id) ? (
                                    <div className="text-xs">
                                      <div>排队中 #{videoQueueInfo.get(shot.id)?.position}</div>
                                      <div>预计 {videoQueueInfo.get(shot.id)?.estimated}</div>
                                    </div>
                                  ) : (
                                    <div className="text-sm">生成视频中...</div>
                                  )}
                                </div>
                              ) : (
                                <>
                                  <Video className="w-5 h-5 mr-2" />
                                  🎬 立即生成视频
                                </>
                              )}
                            </Button>
                            
                            {!shot.english_prompt?.trim() && (
                              <p className="text-orange-400 text-xs mt-2">⚠️ 需要视频提示词</p>
                            )}
                          </div>
                        </div>
                      ) : (
                        <div className={`flex items-center justify-center bg-gray-800/50 border-2 border-dashed border-gray-700 rounded-lg ${getImagePreviewStyle(shot.image_size || selectedImageSize).aspectRatio} max-w-sm mx-auto p-6`}>
                          <div className="text-center">
                            <Video className="w-16 h-16 mx-auto text-gray-500 mb-4" />
                            <p className="text-gray-400 font-medium mb-2">等待图片生成</p>
                            <p className="text-gray-500 text-sm mb-4">需要先生成图片才能制作视频</p>
                            
                            <Button
                              onClick={() => generateImage(shot.id, shot.image_prompt || "")}
                              disabled={generatingImages.has(shot.id) || shot.status === "generating"}
                              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2"
                            >
                              {generatingImages.has(shot.id) ? (
                                <>
                                  <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                                  生成中...
                                </>
                              ) : (
                                <>
                                  <ImageIcon className="w-5 h-5 mr-2" />
                                  先生成图片
                                </>
                              )}
                            </Button>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>

      {/* Smart Modify Dialog */}
      {showSmartModifyDialog && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-900 border border-gray-700 rounded-lg p-6 w-full max-w-2xl mx-4">
            <div className="flex items-center justify-between mb-4">
                              <h3 className="text-lg font-medium text-white flex items-center gap-2">
                  <span className="bg-blue-500/20 text-blue-400 px-2 py-1 rounded text-xs font-semibold">单个镜头</span>
                  智能修改此镜头
                </h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={closeSmartModifyDialog}
                className="text-gray-400 hover:text-white"
              >
                ×
              </Button>
            </div>
            
            <div className="space-y-4">
              <div>
                <Label htmlFor="smartModifyInput" className="text-white">
                  Modification Instructions
                </Label>
                <Textarea
                  id="smartModifyInput"
                  placeholder="例如：将场景改为夜晚、添加雨效、调整人物表情、改变镜头角度等..."
                  value={smartModifyInput}
                  onChange={(e) => setSmartModifyInput(e.target.value)}
                  className="mt-2 min-h-[100px] bg-gray-800 border-gray-700 text-white placeholder:text-gray-500"
                />
                <div className="text-xs text-gray-400 mt-1">
                  描述你希望如何修改这个分镜，AI会智能地调整内容、图片提示词和视频提示词
                </div>
              </div>
              
              <div className="flex justify-between">
                {undoData && undoData.type === 'single' ? (
                  <Button
                    onClick={handleUndo}
                    disabled={isModifying}
                    variant="outline"
                    className="border-green-600 text-green-400 hover:bg-green-600 hover:text-white bg-transparent"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-4 h-4 mr-2">
                      <path d="M3 7v6h6"/>
                      <path d="M21 17a9 9 0 0 0-9-9 9 9 0 0 0-6 2.3L3 13"/>
                    </svg>
                    恢复此镜头
                  </Button>
                ) : (
                  <div></div>
                )}
                
                <div className="flex gap-3">
                  <Button
                    variant="outline"
                    onClick={closeSmartModifyDialog}
                    className="border-gray-700 text-white hover:bg-gray-800"
                    disabled={isModifying}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleSingleShotSmartModify}
                    disabled={!smartModifyInput.trim() || isModifying}
                    className="bg-orange-600 hover:bg-orange-700 text-white"
                  >
                    {isModifying ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Modifying...
                      </>
                    ) : (
                      <>
                        <Sparkles className="w-4 h-4 mr-2" />
                        Smart Modify
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default function StoryboardsPage() {
  return (
    <Suspense fallback={
      <div className="min-h-full bg-gray-950 p-8 flex items-center justify-center">
        <div className="flex items-center gap-2 text-white">
          <Loader2 className="w-6 h-6 animate-spin" />
          <span>Loading storyboards...</span>
        </div>
      </div>
    }>
      <StoryboardsContent />
    </Suspense>
  )
}
